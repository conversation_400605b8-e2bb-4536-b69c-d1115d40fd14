package tmsbrokerstaff

import (
	"context"
	"errors"

	"gorm.io/gorm/clause"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

const (
	EmptyTMSBrokerStaffSlice = "empty slice of TMSBrokerStaff"
)

// Refreshes list of TMS broker staff by upserting current staff data.
func RefreshTMSBrokerStaff(ctx context.Context, staff *[]models.TMSBrokerStaff) error {
	if len(*staff) == 0 {
		return errors.New(EmptyTMSBrokerStaffSlice)
	}

	return rds.WithContext(ctx).
		Clauses(
			clause.OnConflict{
				Columns: []clause.Column{{Name: "tms_integration_id"}, {Name: "external_tms_id"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"organization_id",
					"login",
					"contact_name",
					"email",
					"phone",
					"mobile",
					"fax",
					"title",
					"enabled",
					"address_line1",
					"address_line2",
					"city",
					"state",
					"zipcode",
					"country",
					"reference_number",
					"updated_at",
				}),
			},
		).
		CreateInBatches(&staff, 1000).
		Error
}
