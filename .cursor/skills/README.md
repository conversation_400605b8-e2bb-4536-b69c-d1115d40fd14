# Cursor Skills Guide

[Official Cursor Docs](https://cursor.com/docs/context/skills)

## What Are Skills?

Skills are specialized knowledge modules that teach the AI agent how to perform specific tasks or follow particular patterns. Unlike rules (which are always loaded or file-specific), skills are **dynamically activated** based on context and the task at hand.

## Skills vs Always-On Rules

### Always-On Rules (`.cursor/rules/*.mdc`)

**When to use:**

- Core coding standards that should apply to **every** interaction
- Language-specific conventions (e.g., Go naming, error handling)
- Project-wide patterns that must never be violated
- Rules that need to be checked even when not explicitly mentioned

**Characteristics:**

- Loaded on every request (if `alwaysApply: true`)
- Consume tokens on every interaction
- No context matching required
- Best for "always do this" patterns

**Example:** `go-conventions.mdc` with `alwaysApply: true` - ensures Go best practices are always followed.

### Skills (`.cursor/skills/[skill-name]/SKILL.md`)

**When to use:**

- Specialized workflows or domain knowledge
- Patterns that are only relevant in specific contexts
- Detailed guidance that's not needed for every request
- Complex procedures that benefit from focused attention

**Characteristics:**

- Loaded only when the description matches the task
- More token-efficient for specialized knowledge
- Activated by trigger terms in the description
- Best for "when doing X, follow this pattern"

**Example:** `redis-patterns` skill - only loaded when needing to cache/work with Redis.

## Token Spend & Context Pollution

### Token Efficiency

**Always-On Rules:**

- ✅ **Pro:** Immediate availability, no matching overhead
- ❌ **Con:** Consumes tokens even when irrelevant (e.g., Redis rules loaded during frontend work)
- ❌ **Con:** Can bloat context window with unused information

**Skills:**

- ✅ **Pro:** Only loaded when needed, saving tokens
- ✅ **Pro:** Keeps context focused on relevant information
- ❌ **Con:** Requires good description matching to activate
- ❌ **Con:** May miss edge cases if description doesn't match

### Context Pollution

**Always-On Rules:**

- Risk: Too many always-on rules = cluttered context
- Impact: Agent may focus on wrong patterns
- Solution: Keep always-on rules minimal (3-5 max)

**Skills:**

- Benefit: Context stays clean and focused
- Risk: Over-specific descriptions = missed activations
- Solution: Write descriptions with multiple trigger terms

## Creating Skills

### Structure

```
.cursor/skills/
└── skill-name/
    └── SKILL.md          # Required - main instructions
```

### SKILL.md Format

```markdown
---
name: skill-name
description: What this skill does and when to use it. Include trigger terms.
---

# Skill Name

## Instructions

Clear, actionable guidance...

## Examples

Concrete examples...
```

### Key Requirements

1. **Name:** Lowercase, hyphens, max 64 chars (e.g., `redis-patterns`)
2. **Description:**
   - Write in third person ("Enforces X...")
   - Include WHAT and WHEN
   - Add trigger terms (keywords that activate the skill)
   - Max 1024 characters
3. **Content:** Keep SKILL.md under 500 lines (use progressive disclosure)

### Description Best Practices

**Good:**

```yaml
description: Enforces using Redis wrapper functions from common/redis instead of direct Redis client access. Use when writing or reviewing code that interacts with Redis, when the user mentions Redis operations, or when you see direct redis.RDB access patterns.
```

**Bad:**

```yaml
description: Redis stuff
```

The good description includes:

- ✅ What it does (enforces wrapper functions)
- ✅ When to use (writing/reviewing Redis code)
- ✅ Multiple trigger terms (Redis, redis.RDB, wrapper functions)

## Decision Framework

### Use Always-On Rule When:

- [ ] Pattern applies to 80%+ of interactions
- [ ] Violation would cause serious issues
- [ ] Pattern is simple and concise (< 100 lines)
- [ ] No context matching needed

**Example:** Go naming conventions, line length limits

### Use Skill When:

- [ ] Pattern is domain-specific (Redis, testing, API design)
- [ ] Only relevant in certain contexts
- [ ] Detailed guidance (> 100 lines)
- [ ] Can be described with clear trigger terms

**Example:** Redis patterns, code review workflows, complex testing patterns

## Migration Strategy

**Converting Rules to Skills:**

1. If rule has `alwaysApply: false` → Good candidate for skill
2. If rule is rarely relevant → Convert to skill
3. If rule is very detailed → Consider skill with progressive disclosure
4. Keep core patterns as always-on rules

**Example:** `redis-patterns.mdc` with `alwaysApply: false` → Converted to `redis-patterns` skill

## Best Practices

1. **Start with rules** for core patterns, convert to skills when they become specialized
2. **Monitor token usage** - if always-on rules exceed ~2000 tokens, consider converting some to skills
3. **Test skill activation** - verify descriptions trigger when expected
4. **Keep skills focused** - one skill = one domain/pattern
5. **Update descriptions** - refine trigger terms based on missed activations

## Current Skills

- `redis-patterns` - Redis wrapper function patterns and anti-patterns

## References

- [Cursor Skills Documentation](https://cursor.com/docs/context/skills)
- See `.cursor/rules/` for always-on rules
- See `~/.cursor/skills-cursor/` for built-in Cursor skills (do not modify)
