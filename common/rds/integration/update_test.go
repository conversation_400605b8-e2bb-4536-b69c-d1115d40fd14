package integration

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func TestUpdateStarkCredentials(t *testing.T) {
	ctx := context.Background()
	// Initialize DB for testing - assuming RDS is set up for tests
	// In some environments, we might need to skip if DB is not available
	// But let's assume we can run it or it will be skipped by test runner if no DB

	// Create a test integration
	tms := models.Integration{
		Name:      models.Stark,
		Type:      models.TMS,
		ServiceID: 1,
		Username:  "testuser",
	}

	db := rds.WithContext(ctx)
	if db == nil {
		t.Skip("DB not initialized")
	}

	err := db.Create(&tms).Error
	require.NoError(t, err)
	defer db.Unscoped().Delete(&tms)

	// Update credentials
	newUserID := "new-user-id"
	newRogersRevision := "new-rogers-revision"
	apiKey := "new-api-key"

	err = UpdateStarkCredentials(ctx, tms.ID, newUserID, newRogersRevision, apiKey)
	assert.NoError(t, err)

	// Verify updates
	var updatedTms models.Integration
	err = db.First(&updatedTms, tms.ID).Error
	require.NoError(t, err)

	assert.Equal(t, newUserID, updatedTms.UserID)
	assert.Equal(t, newRogersRevision, updatedTms.RogersRevision)
	assert.Equal(t, apiKey, updatedTms.APIKey)
}
