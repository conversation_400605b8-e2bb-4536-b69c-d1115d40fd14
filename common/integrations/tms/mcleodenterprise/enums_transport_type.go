package mcleodenterprise

import (
	"fmt"
	"log"
	"regexp"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
)

// Map of transport types to corresponding trailer code
func (m *McleodEnterprise) getTrailerCode(input string) (string, error) {
	tenant := strings.ToLower(m.tms.Tenant)
	switch {
	case strings.Contains(tenant, TenantTrident):
		return toTridentTrailerCode(input)
	case strings.Contains(tenant, TenantFetch):
		return toFetchFreightTrailerCode(input)
	case strings.Contains(tenant, TenantSyfan):
		return toSyfanTrailerCode(input)
	case strings.Contains(tenant, TenantTumalo):
		return toTumaloTrailerCode(input)
	default:
		return "", m.unknownTenantError("trailer type")
	}
}

// Maps TMS transport types to TransportTypeEnum to support win rate & lane history calculations
func (m *McleodEnterprise) MapTransportTypeEnum(transportType string) (models.TransportType, error) {
	tenant := strings.ToLower(m.tms.Tenant)
	transportType = strings.TrimSpace(strings.ToLower(transportType))

	switch {
	case strings.Contains(tenant, TenantTrident):
		return mapTridentTransportTypeEnum(transportType)

	case strings.Contains(tenant, TenantFetch):
		return mapFetchTransportTypeEnum(transportType)

	default:
		// Use existing maps as fallback
		res, err := mapTridentTransportTypeEnum(transportType)
		if err == nil {
			return res, nil
		}

		res, err = mapFetchTransportTypeEnum(transportType)
		if err == nil {
			return res, nil
		}

		return "", m.unknownTenantError("transport type")
	}
}

// Trailer code is optional
func toTridentTrailerCode(input string) (string, error) {
	input = strings.TrimSpace(strings.ToLower(input))

	var trailerCodeMap = map[string]string{
		"":                                    "",
		"26b":                                 "26B",
		"26'":                                 "26B",
		"53f":                                 "53F",
		"53ft flatbed":                        "53F",
		"ac":                                  "AC",
		"auto carrier":                        "AC",
		"b":                                   "B",
		"beam trailer":                        "B",
		"cn":                                  "CN",
		"conestoga (dat)":                     "CN",
		"c":                                   "C",
		"container":                           "C",
		"cr":                                  "CR",
		"container refrigerated (dat)":        "CR",
		"ci":                                  "CI",
		"container, insulated (dat)":          "CI",
		"cv":                                  "CV",
		"conveyor (dat)":                      "CV",
		"dd":                                  "DD",
		"double drop (dat)":                   "DD",
		"ddt":                                 "DDT",
		"drop deck trailers (dat)":            "DDT",
		"dl":                                  "DL",
		"drop deck, landoll (dat)":            "DL",
		"dt":                                  "DT",
		"dump trailer (dat)":                  "DT",
		"fr":                                  "FR",
		"flat/van/reefer (dat)":               "FR",
		"f":                                   "F",
		"flatbed (dat)":                       "F",
		"fa":                                  "FA",
		"flatbed airride (dat)":               "FA",
		"bt":                                  "BT",
		"flatbed b-train (dat)":               "BT",
		"fn":                                  "FN",
		"flatbed conestoga (dat)":             "FN",
		"fz":                                  "FZ",
		"flatbed hazmat (dat)":                "FZ",
		"fh":                                  "FH",
		"flatbed hotshot (dat)":               "FH",
		"mx":                                  "MX",
		"flatbed maxi (dat)":                  "MX",
		"fmo":                                 "FMO",
		"flatbed moffett (dat)":               "FMO",
		"fd":                                  "FD",
		"flatbed or step deck (dat)":          "FD",
		"fo":                                  "FO",
		"flatbed over-dimension (dat)":        "FO",
		"fte":                                 "FTE",
		"flatbed tanker endorsed (dat)":       "FTE",
		"fm":                                  "FM",
		"flatbed w/ team (dat)":               "FM",
		"ft":                                  "FT",
		"flatbed w/tarps (dat)":               "FT",
		"fc":                                  "FC",
		"flatbed with chains (dat)":           "FC",
		"log":                                 "LOG",
		"forestry trailers":                   "LOG",
		"hb":                                  "HB",
		"hopper bottom (dat)":                 "HB",
		"ir":                                  "IR",
		"insulated van or reefer (dat)":       "IR",
		"lb":                                  "LB",
		"lowboy (dat)":                        "LB",
		"lr":                                  "LR",
		"lowboy or rgn (dat)":                 "LR",
		"lo":                                  "LO",
		"lowboy overdimension (dat)":          "LO",
		"mv":                                  "MV",
		"moving van (dat)":                    "MV",
		"o":                                   "O",
		"other":                               "O",
		"nu":                                  "NU",
		"pneumatic (dat)":                     "NU",
		"po":                                  "PO",
		"power only (dat)":                    "PO",
		"r":                                   "R",
		"reefer (dat)":                        "R",
		"ra":                                  "RA",
		"reefer airride (dat)":                "RA",
		"r2":                                  "R2",
		"reefer doubles (dat)":                "R2",
		"rz":                                  "RZ",
		"reefer hazmat (dat)":                 "RZ",
		"ri":                                  "RI",
		"reefer intermodal (dat)":             "RI",
		"rn":                                  "RN",
		"reefer logistics (dat)":              "RN",
		"rv":                                  "RV",
		"reefer or vented van (dat)":          "RV",
		"rp":                                  "RP",
		"reefer pallet exchange (dat)":        "RP",
		"rte":                                 "RTE",
		"reefer tanker endorsed (dat)":        "RTE",
		"rteh":                                "RTEH",
		"reefer tanker endorsed hazmat (dat)": "RTEH",
		"rm":                                  "RM",
		"reefer w/ team (dat)":                "RM",
		"rg":                                  "RG",
		"removable gooseneck (dat)":           "RG",
		"spr":                                 "SPR",
		"sprinter":                            "SPR",
		"sd":                                  "SD",
		"step deck (dat)":                     "SD",
		"sr":                                  "SR",
		"step deck or rgn (dat)":              "SR",
		"sn":                                  "SN",
		"stepdeck conestoga (dat)":            "SN",
		"sb":                                  "SB",
		"straight box truck":                  "SB",
		"sbt":                                 "SBT",
		"straight box trucks":                 "SBT",
		"st":                                  "ST",
		"stretch trailer (dat)":               "ST",
		"ta":                                  "TA",
		"tanker aluminum (dat)":               "TA",
		"tn":                                  "TN",
		"tanker intermodal (dat)":             "TN",
		"ts":                                  "TS",
		"tanker steel (dat)":                  "TS",
		"tt":                                  "TT",
		"truck and trailer (dat)":             "TT",
		"v":                                   "V",
		"van (dat)":                           "V",
		"va":                                  "VA",
		"van airride (dat)":                   "VA",
		"vw":                                  "VW",
		"van blanket wrap":                    "VW",
		"vs":                                  "VS",
		"van conestoga (dat)":                 "VS",
		"vc":                                  "VC",
		"van curtain (dat)":                   "VC",
		"v2":                                  "V2",
		"van double (dat)":                    "V2",
		"vz":                                  "VZ",
		"van hazmat (dat)":                    "VZ",
		"vh":                                  "VH",
		"van hotshot (dat)":                   "VH",
		"vi":                                  "VI",
		"van insulated (dat)":                 "VI",
		"vn":                                  "VN",
		"van intermodal (dat)":                "VN",
		"vg":                                  "VG",
		"van lift-gate (dat)":                 "VG",
		"vl":                                  "VL",
		"van logistics (dat)":                 "VL",
		"ot":                                  "OT",
		"van opentop (dat)":                   "OT",
		"vf":                                  "VF",
		"van or flatbed (dat)":                "VF",
		"vt":                                  "VT",
		"van or flats w/tarps (dat)":          "VT",
		"vr":                                  "VR",
		"van or reefer (dat)":                 "VR",
		"vp":                                  "VP",
		"van pallet exchange (dat)":           "VP",
		"vb":                                  "VB",
		"van roller bed (dat)":                "VB",
		"vte":                                 "VTE",
		"van tanker endorsed (dat)":           "VTE",
		"vteh":                                "VTEH",
		"van tanker endorsed hazmat (dat)":    "VTEH",
		"vv":                                  "VV",
		"van vented (dat)":                    "VV",
		"vm":                                  "VM",
		"van w/ team (dat)":                   "VM",
	}

	if code, ok := trailerCodeMap[input]; ok {
		return code, nil
	}

	return "", fmt.Errorf("unsupported trailer type: %s", input)
}

func toFetchFreightTrailerCode(input string) (string, error) {
	var trailerCodeMap = map[string]string{
		"ac":                            "AC",
		"auto carrier (dat)":            "AC",
		"cn":                            "CN",
		"conestoga (dat)":               "CN",
		"c":                             "C",
		"container (dat)":               "C",
		"cr":                            "CR",
		"container refrigerated (dat)":  "CR",
		"ci":                            "CI",
		"container, insulated (dat)":    "CI",
		"cv":                            "CV",
		"conveyor (dat)":                "CV",
		"dd":                            "DD",
		"double drop (dat)":             "DD",
		"ddt":                           "DDT",
		"drop deck trailers":            "DDT",
		"la":                            "LA",
		"drop deck, landoll (dat)":      "LA",
		"dt":                            "DT",
		"dump trailer (dat)":            "DT",
		"fr":                            "FR",
		"flat/van/reefer (dat)":         "FR",
		"f":                             "F",
		"flatbed (dat)":                 "F",
		"fa":                            "FA",
		"flatbed airride (dat)":         "FA",
		"bt":                            "BT",
		"flatbed b-train (dat)":         "BT",
		"fn":                            "FN",
		"flatbed conestoga (dat)":       "FN",
		"fz":                            "FZ",
		"flatbed hazmat (dat)":          "FZ",
		"fh":                            "FH",
		"flatbed hotshot (dat)":         "FH",
		"mx":                            "MX",
		"flatbed maxi (dat)":            "MX",
		"fd":                            "FD",
		"flatbed or step deck (dat)":    "FD",
		"fo":                            "FO",
		"flatbed over-dimension":        "FO",
		"fm":                            "FM",
		"flatbed w/ team (dat)":         "FM",
		"ft":                            "FT",
		"flatbed w/tarps (dat)":         "FT",
		"fc":                            "FC",
		"flatbed with chains (dat)":     "FC",
		"log":                           "LOG",
		"forestry trailers":             "LOG",
		"hb":                            "HB",
		"hopper bottom (dat)":           "HB",
		"im10":                          "IM10",
		"imdl container 10'":            "IM10",
		"im20":                          "IM20",
		"imdl container 20'":            "IM20",
		"im40":                          "IM40",
		"imdl container 40'":            "IM40",
		"im45":                          "IM45",
		"imdl container 45'":            "IM45",
		"im48":                          "IM48",
		"imdl container 48'":            "IM48",
		"im53":                          "IM53",
		"imdl container 53'":            "IM53",
		"ir":                            "IR",
		"insulated van or reefer (dat)": "IR",
		"lb":                            "LB",
		"lowboy (dat)":                  "LB",
		"lr":                            "LR",
		"lowboy or rgn (dat)":           "LR",
		"lo":                            "LO",
		"lowboy overdimension (dat)":    "LO",
		"mv":                            "MV",
		"moving van (dat)":              "MV",
		"o":                             "O",
		"other":                         "O",
		"nu":                            "NU",
		"pneumatic (dat)":               "NU",
		"epo":                           "EPO",
		"power only (dat)":              "EPO",
		"r":                             "R",
		"reefer (dat)":                  "R",
		"ra":                            "RA",
		"reefer airride (dat)":          "RA",
		"r2":                            "R2",
		"reefer doubles (dat)":          "R2",
		"rz":                            "RZ",
		"reefer hazmat (dat)":           "RZ",
		"rn":                            "RN",
		"reefer intermodal (dat)":       "RN",
		"rl":                            "RL",
		"reefer logistics (dat)":        "RL",
		"rv":                            "RV",
		"reefer or vented van":          "RV",
		"rp":                            "RP",
		"reefer pallet exchange (dat)":  "RP",
		"rm":                            "RM",
		"reefer w/ team (dat)":          "RM",
		"rg":                            "RG",
		"removable gooseneck (dat)":     "RG",
		"sd":                            "SD",
		"step deck (dat)":               "SD",
		"sr":                            "SR",
		"step deck or rgn":              "SR",
		"sn":                            "SN",
		"stepdeck conestoga (dat)":      "SN",
		"sb":                            "SB",
		"straight box truck":            "SB",
		"sbt":                           "SBT",
		"straight box trucks":           "SBT",
		"st":                            "ST",
		"stretch trailer (dat)":         "ST",
		"ta":                            "TA",
		"tanker aluminum (dat)":         "TA",
		"tn":                            "TN",
		"tanker intermodal (dat)":       "TN",
		"ts":                            "TS",
		"tanker steel (dat)":            "TS",
		"tt":                            "TT",
		"truck and trailer (dat)":       "TT",
		"v":                             "V",
		"van (dat)":                     "V",
		"va":                            "VA",
		"van airride (dat)":             "VA",
		"vw":                            "VW",
		"van blanket wrap":              "VW",
		"vs":                            "VS",
		"van conestoga (dat)":           "VS",
		"vc":                            "VC",
		"van curtain (dat)":             "VC",
		"v2":                            "V2",
		"van double (dat)":              "V2",
		"vz":                            "VZ",
		"van hazmat (dat)":              "VZ",
		"vh":                            "VH",
		"van hotshot (dat)":             "VH",
		"vi":                            "VI",
		"van insulated (dat)":           "VI",
		"vn":                            "VN",
		"van intermodal (dat)":          "VN",
		"vg":                            "VG",
		"van lift-gate (dat)":           "VG",
		"vl":                            "VL",
		"van logistics (dat)":           "VL",
		"vltl":                          "VLTL",
		"van ltl":                       "VLTL",
		"ot":                            "OT",
		"van opentop (dat)":             "OT",
		"vf":                            "VF",
		"van or flatbed (dat)":          "VF",
		"vt":                            "VT",
		"van or flats w/tarps (dat)":    "VT",
		"vr":                            "VR",
		"van or reefer (dat)":           "VR",
		"vp":                            "VP",
		"van pallet exchange (dat)":     "VP",
		"vb":                            "VB",
		"van roller bed (dat)":          "VB",
		"vv":                            "VV",
		"van vented (dat)":              "VV",
		"vm":                            "VM",
		"van w/ team (dat)":             "VM",
	}

	input = strings.TrimSpace(strings.ToLower(input))

	if code, ok := trailerCodeMap[input]; ok {
		return code, nil
	}

	return "", fmt.Errorf("unsupported trailer type: %s", input)
}

func toSyfanTrailerCode(input string) (string, error) {
	input = strings.TrimSpace(strings.ToLower(input))

	if code, ok := syfanTrailerLabelToCodeMap[input]; ok {
		return code, nil
	}

	if _, ok := syfanTrailerCodeToLabelMap[input]; ok {
		return strings.ToUpper(input), nil
	}

	// Check if input is label without code prepended, workaround for duplicate labels with different codes
	re := regexp.MustCompile(`\s+\-\s+`)
	for label, code := range syfanTrailerLabelToCodeMap {
		splits := re.Split(label, 2)
		if len(splits) > 1 {
			if strings.EqualFold(strings.TrimSpace(splits[1]), input) {
				return strings.ToUpper(code), nil
			}
		}
	}

	return "", fmt.Errorf("unsupported trailer type: %s", input)
}

func toTumaloTrailerCode(input string) (string, error) {
	normalizedInput := strings.ToLower(strings.TrimSpace(input))

	// in some very rare cases when an entire lable is sent to the backend as input stribg
	if code, ok := tumaloTrailerLabelToCodeMap[input]; ok {
		return strings.ToUpper(code), nil
	}

	if _, ok := tumaloTrailerCodeToLabelMap[strings.ToUpper(input)]; ok {
		return strings.ToUpper(input), nil
	}
	// string everything after the "-" and check for label IF FE sends an entire label in a rare case.
	for label, code := range tumaloTrailerLabelToCodeMap {
		if idx := strings.Index(label, "-"); idx != -1 {
			label = label[idx+1:]
		}
		strippedLabel := strings.ToLower(strings.TrimSpace(label))
		if strippedLabel == normalizedInput {
			log.Println("Found match")
			return strings.ToUpper(code), nil
		}
	}

	return "", fmt.Errorf("unsupported trailer type: %s", input)
}

// NOTE: Because of duplicate descriptions that map to different codes,
// we manually concatenate the code to the description here and in FE dropdowns to create a valid map with unique keys.
var syfanTrailerLabelToCodeMap = map[string]string{
	"ups - ups":                                       "UPS",
	"v12 - 12 ft van":                                 "V12",
	"16lg - 16 foot boxtruck w/ liftgate":             "16LG",
	"vs20 - 20 foot straight truck":                   "VS20",
	"20c - 20 ft container":                           "20C",
	"20cw - 20 ft container overweight":               "20CW",
	"vs22 - 22 foot box truck-9 years old or newer":   "VS22",
	"24lg - 24 foot boxtruck - lift gate":             "24LG",
	"v24 - 24 foot van - 9 years old or newer":        "V24",
	"26sg - 26 foot straight truck with lift gate":    "26SG",
	"v28 - 28 foot van":                               "V28",
	"v40 - 40 foot dry van trailer":                   "V40",
	"40c - 40 ft container":                           "40C",
	"40cw - 40 ft container overweight":               "40CW",
	"v45 - 45 foot dry van trailer":                   "V45",
	"48lg - 48 ft trl with lift gate":                 "48LG",
	"53bp - 53 foot bulletproof van":                  "53BP",
	"sd53 - 53 foot step deck":                        "SD53",
	"v53t - 53 foot van with team":                    "V53T",
	"53hd - 53 ft heavy duty van":                     "53HD",
	"53lg - 53 ft van with lift gate":                 "53LG",
	"v53l - 53 ft van- local moves":                   "V53L",
	"sv53 - 53' super van":                            "SV53",
	"r53n - 9 years old or newer 53' refr":            "R53N",
	"v53n - 9 years old or newer 53' van":             "V53N",
	"v53m - 9 years old or newer 53' van mexico rout": "V53M",
	"arbt - air ride box truck":                       "ARBT",
	"ac - auto carrier (dat)":                         "AC",
	"bt12 - box truck 12 foot":                        "BT12",
	"cvan - cargo van":                                "CVAN",
	"con - conestoga":                                 "CON",
	"cn - conestoga (dat)":                            "CN",
	"co48 - conestoga 48 foot":                        "CO48",
	"c053 - conestoga 53 foot":                        "C053",
	"c - container (dat)":                             "C",
	"cr - container refrigerated (dat)":               "CR",
	"ci - container, insulated (dat)":                 "CI",
	"cv - conveyor (dat)":                             "CV",
	"st12 - dock high straight truck 12ft":            "ST12",
	"dd - double drop (dat)":                          "DD",
	"ddt - drop deck trailers":                        "DDT",
	"la - drop deck, landoll (dat)":                   "LA",
	"dt - dump trailer (dat)":                         "DT",
	"53a5 - fca 53 dv shape":                          "53A5",
	"53a2 - fca 53 dv sterling heights":               "53A2",
	"fr - flat/van/reefer (dat)":                      "FR",
	"f - flatbed (dat)":                               "F",
	"f25 - flatbed 25ft":                              "F25",
	"f48 - flatbed 48 ft":                             "F48",
	"f53 - flatbed 53 ft":                             "F53",
	"fa - flatbed airride (dat)":                      "FA",
	"bt - flatbed b-train (dat)":                      "BT",
	"fn - flatbed conestoga (dat)":                    "FN",
	"fz - flatbed hazmat (dat)":                       "FZ",
	"fh - flatbed hotshot (dat)":                      "FH",
	"mx - flatbed maxi (dat)":                         "MX",
	"fd - flatbed or step deck (dat)":                 "FD",
	"fo - flatbed over-dimension":                     "FO",
	"fm - flatbed w/ team (dat)":                      "FM",
	"ft - flatbed w/tarps (dat)":                      "FT",
	"fc - flatbed with chains (dat)":                  "FC",
	"fvsd - flatbed/van/stepdeck":                     "FVSD",
	"hcv - high cube 53' dry van":                     "HCV",
	"hb - hopper bottom (dat)":                        "HB",
	"hs - hotshot":                                    "HS",
	"ir - insulated van or reefer (dat)":              "IR",
	"im - intermodal trailers":                        "IM",
	"ltt - load to truck - nestle drop":               "LTT",
	"lb - lowboy (dat)":                               "LB",
	"lr - lowboy or rgn (dat)":                        "LR",
	"lo - lowboy overdimension (dat)":                 "LO",
	"lb55 - lowboy trailer, 55 ton":                   "LB55",
	"mv - moving van (dat)":                           "MV",
	"o - other":                                       "O",
	"par - partial truck load- mixed freight":         "PAR",
	"nu - pneumatic (dat)":                            "NU",
	"po - power only (dat)":                           "PO",
	"po-d - power only - doubles":                     "PO-D",
	"po-h - power only - hazmat":                      "PO-H",
	"po-t - power only - team":                        "PO-T",
	"lm - power only local":                           "LM",
	"4lm - power only local moves":                    "4LM",
	"r - reefer (dat)":                                "R",
	"r48 - reefer 48 ft":                              "R48",
	"r53 - reefer 53 ft":                              "R53",
	"r53d - reefer 53 ft":                             "R53D",
	"ra - reefer airride (dat)":                       "RA",
	"r2 - reefer doubles (dat)":                       "R2",
	"rz - reefer hazmat (dat)":                        "RZ",
	"rn - reefer intermodal (dat)":                    "RN",
	"rl - reefer logistics (dat)":                     "RL",
	"rv - reefer or vented van":                       "RV",
	"rp - reefer pallet exchange (dat)":               "RP",
	"rm - reefer w/ team (dat)":                       "RM",
	"rg - removable gooseneck (dat)":                  "RG",
	"sw - southwest carrier":                          "SW",
	"swr - southwest reefer carrier":                  "SWR",
	"sv - sprinter van":                               "SV",
	"sd - step deck (dat)":                            "SD",
	"sr - step deck or rgn":                           "SR",
	"sn - stepdeck conestoga (dat)":                   "SN",
	"sb - straight box truck":                         "SB",
	"st - strectch trailer (dat)":                     "ST",
	"ta - tanker aluminum (dat)":                      "TA",
	"tn - tanker intermodal (dat)":                    "TN",
	"ts - tanker steel (dat)":                         "TS",
	"team - teams available":                          "TEAM",
	"la50 - traveling axle trailer, landoll (cali)":   "LA50",
	"la53 - traveling axle trailer, landoll w/ winch": "LA53",
	"la41 - traveling axle trailer, landoll w/ winch": "LA41",
	"la48 - traveling axle trailer, landoll w/ winch": "LA48",
	"tt - truck and trailer (dat)":                    "TT",
	"ps15 - ups peak 2015":                            "PS15",
	"v - van (dat)":                                   "V",
	"v48 - van 48 ft":                                 "V48",
	"v5nt - van 53 9 years or newer/team needed":      "V5NT",
	"v53d - van 53 ft":                                "V53D",
	"v53 - van 53 ft":                                 "V53",
	"va - van airride (dat)":                          "VA",
	"vw - van blanket wrap":                           "VW",
	"vs - van conestoga (dat)":                        "VS",
	"vc - van curtain (dat)":                          "VC",
	"v2 - van double (dat)":                           "V2",
	"vz - van hazmat (dat)":                           "VZ",
	"vh - van hotshot (dat)":                          "VH",
	"vi - van insulated (dat)":                        "VI",
	"vn - van intermodal (dat)":                       "VN",
	"vg - van lift-gate (dat)":                        "VG",
	"vl - van logistics (dat)":                        "VL",
	"ot - van opentop (dat)":                          "OT",
	"vf - van or flatbed (dat)":                       "VF",
	"vt - van or flats w/tarps (dat)":                 "VT",
	"vr - van or reefer (dat)":                        "VR",
	"vr48 - van or reefer 48 ft":                      "VR48",
	"vr53 - van or reefer 53 ft":                      "VR53",
	"vrtm - van or reefer 53 with team":               "VRTM",
	"vp - van pallet exchange (dat)":                  "VP",
	"vb - van roller bed (dat)":                       "VB",
	"vsp - van sprinter":                              "VSP",
	"v53a - van trailer air ride 53ft":                "V53A",
	"vv - van vented (dat)":                           "VV",
	"vm - van w/ team (dat)":                          "VM",
}

var syfanTrailerCodeToLabelMap = map[string]string{
	"ups":  "ups - ups",
	"v12":  "v12 - 12 ft van",
	"16lg": "16lg - 16 foot boxtruck w/ liftgate",
	"vs20": "vs20 - 20 foot straight truck",
	"20c":  "20c - 20 ft container",
	"20cw": "20cw - 20 ft container overweight",
	"vs22": "vs22 - 22 foot box truck-9 years old or newer",
	"24lg": "24lg - 24 foot boxtruck - lift gate",
	"v24":  "v24 - 24 foot van - 9 years old or newer",
	"26sg": "26sg - 26 foot straight truck with lift gate",
	"v28":  "v28 - 28 foot van",
	"v40":  "v40 - 40 foot dry van trailer",
	"40c":  "40c - 40 ft container",
	"40cw": "40cw - 40 ft container overweight",
	"v45":  "v45 - 45 foot dry van trailer",
	"48lg": "48lg - 48 ft trl with lift gate",
	"53bp": "53bp - 53 foot bulletproof van",
	"sd53": "sd53 - 53 foot step deck",
	"v53t": "v53t - 53 foot van with team",
	"53hd": "53hd - 53 ft heavy duty van",
	"53lg": "53lg - 53 ft van with lift gate",
	"v53l": "v53l - 53 ft van- local moves",
	"sv53": "sv53 - 53' super van",
	"r53n": "r53n - 9 years old or newer 53' refr",
	"v53n": "v53n - 9 years old or newer 53' van",
	"v53m": "v53m - 9 years old or newer 53' van mexico rout",
	"arbt": "arbt - air ride box truck",
	"ac":   "ac - auto carrier (dat)",
	"bt12": "bt12 - box truck 12 foot",
	"cvan": "cvan - cargo van",
	"con":  "con - conestoga",
	"cn":   "cn - conestoga (dat)",
	"co48": "co48 - conestoga 48 foot",
	"c053": "c053 - conestoga 53 foot",
	"c":    "c - container (dat)",
	"cr":   "cr - container refrigerated (dat)",
	"ci":   "ci - container, insulated (dat)",
	"cv":   "cv - conveyor (dat)",
	"st12": "st12 - dock high straight truck 12ft",
	"dd":   "dd - double drop (dat)",
	"ddt":  "ddt - drop deck trailers",
	"la":   "la - drop deck, landoll (dat)",
	"dt":   "dt - dump trailer (dat)",
	"53a5": "53a5 - fca 53 dv shape",
	"53a2": "53a2 - fca 53 dv sterling heights",
	"fr":   "fr - flat/van/reefer (dat)",
	"f":    "f - flatbed (dat)",
	"f25":  "f25 - flatbed 25ft",
	"f48":  "f48 - flatbed 48 ft",
	"f53":  "f53 - flatbed 53 ft",
	"fa":   "fa - flatbed airride (dat)",
	"bt":   "bt - flatbed b-train (dat)",
	"fn":   "fn - flatbed conestoga (dat)",
	"fz":   "fz - flatbed hazmat (dat)",
	"fh":   "fh - flatbed hotshot (dat)",
	"mx":   "mx - flatbed maxi (dat)",
	"fd":   "fd - flatbed or step deck (dat)",
	"fo":   "fo - flatbed over-dimension",
	"fm":   "fm - flatbed w/ team (dat)",
	"ft":   "ft - flatbed w/tarps (dat)",
	"fc":   "fc - flatbed with chains (dat)",
	"fvsd": "fvsd - flatbed/van/stepdeck",
	"hcv":  "hcv - high cube 53' dry van",
	"hb":   "hb - hopper bottom (dat)",
	"hs":   "hs - hotshot",
	"ir":   "ir - insulated van or reefer (dat)",
	"im":   "im - intermodal trailers",
	"ltt":  "ltt - load to truck - nestle drop",
	"lb":   "lb - lowboy (dat)",
	"lr":   "lr - lowboy or rgn (dat)",
	"lo":   "lo - lowboy overdimension (dat)",
	"lb55": "lb55 - lowboy trailer, 55 ton",
	"mv":   "mv - moving van (dat)",
	"o":    "o - other",
	"par":  "par - partial truck load- mixed freight",
	"nu":   "nu - pneumatic (dat)",
	"po":   "po - power only (dat)",
	"po-d": "po-d - power only - doubles",
	"po-h": "po-h - power only - hazmat",
	"po-t": "po-t - power only - team",
	"lm":   "lm - power only local",
	"4lm":  "4lm - power only local moves",
	"r":    "r - reefer (dat)",
	"r48":  "r48 - reefer 48 ft",
	"r53":  "r53 - reefer 53 ft",
	"r53d": "r53d - reefer 53 ft",
	"ra":   "ra - reefer airride (dat)",
	"r2":   "r2 - reefer doubles (dat)",
	"rz":   "rz - reefer hazmat (dat)",
	"rn":   "rn - reefer intermodal (dat)",
	"rl":   "rl - reefer logistics (dat)",
	"rv":   "rv - reefer or vented van",
	"rp":   "rp - reefer pallet exchange (dat)",
	"rm":   "rm - reefer w/ team (dat)",
	"rg":   "rg - removable gooseneck (dat)",
	"sw":   "sw - southwest carrier",
	"swr":  "swr - southwest reefer carrier",
	"sv":   "sv - sprinter van",
	"sd":   "sd - step deck (dat)",
	"sr":   "sr - step deck or rgn",
	"sn":   "sn - stepdeck conestoga (dat)",
	"sb":   "sb - straight box truck",
	"st":   "st - strectch trailer (dat)",
	"ta":   "ta - tanker aluminum (dat)",
	"tn":   "tn - tanker intermodal (dat)",
	"ts":   "ts - tanker steel (dat)",
	"team": "team - teams available",
	"la50": "la50 - traveling axle trailer, landoll (cali)",
	"la53": "la53 - traveling axle trailer, landoll w/ winch",
	"la41": "la41 - traveling axle trailer, landoll w/ winch",
	"la48": "la48 - traveling axle trailer, landoll w/ winch",
	"tt":   "tt - truck and trailer (dat)",
	"ps15": "ps15 - ups peak 2015",
	"v":    "v - van (dat)",
	"v48":  "v48 - van 48 ft",
	"v5nt": "v5nt - van 53 9 years or newer/team needed",
	"v53d": "v53d - van 53 ft",
	"v53":  "v53 - van 53 ft",
	"va":   "va - van airride (dat)",
	"vw":   "vw - van blanket wrap",
	"vs":   "vs - van conestoga (dat)",
	"vc":   "vc - van curtain (dat)",
	"v2":   "v2 - van double (dat)",
	"vz":   "vz - van hazmat (dat)",
	"vh":   "vh - van hotshot (dat)",
	"vi":   "vi - van insulated (dat)",
	"vn":   "vn - van intermodal (dat)",
	"vg":   "vg - van lift-gate (dat)",
	"vl":   "vl - van logistics (dat)",
	"ot":   "ot - van opentop (dat)",
	"vf":   "vf - van or flatbed (dat)",
	"vt":   "vt - van or flats w/tarps (dat)",
	"vr":   "vr - van or reefer (dat)",
	"vr48": "vr48 - van or reefer 48 ft",
	"vr53": "vr53 - van or reefer 53 ft",
	"vrtm": "vrtm - van or reefer 53 with team",
	"vp":   "vp - van pallet exchange (dat)",
	"vb":   "vb - van roller bed (dat)",
	"vsp":  "vsp - van sprinter",
	"v53a": "v53a - van trailer air ride 53ft",
	"vv":   "vv - van vented (dat)",
	"vm":   "vm - van w/ team (dat)",
}

var tumaloTrailerLabelToCodeMap = map[string]string{
	"vr53 - 53 foot dry van or reefer":  "VR53",
	"v53 - 53' van":                     "V53",
	"acp - air - cargo plane":           "ACP",
	"ac - auto carrier (dat)":           "AC",
	"cn - conestoga (dat)":              "CN",
	"c - container (dat)":               "C",
	"cr - container refrigerated (dat)": "CR",
	"ci - container, insulated (dat)":   "CI",
	"cv - conveyor (dat)":               "CV",
	"dd - double drop (dat)":            "DD",
	"la - drop deck, landoll (dat)":     "LA",
	"dt - dump trailer (dat)":           "DT",
	"fr - flat/van/reefer (dat)":        "FR",
	"f - flatbed (dat)":                 "F",
	"fa - flatbed airride (dat)":        "FA",
	"bt - flatbed b-train (dat)":        "BT",
	"fn - flatbed conestoga (dat)":      "FN",
	"fz - flatbed hazmat (dat)":         "FZ",
	"fh - flatbed hotshot (dat)":        "FH",
	"mx - flatbed maxi (dat)":           "MX",
	"fd - flatbed or step deck (dat)":   "FD",
	"fo - flatbed over-dimension":       "FO",
	"fm - flatbed w/ team (dat)":        "FM",
	"fs - flatbed w/sides":              "FS",
	"ft - flatbed w/tarps (dat)":        "FT",
	"fc - flatbed with chains (dat)":    "FC",
	"hb - hopper bottom (dat)":          "HB",
	"lb - lowboy (dat)":                 "LB",
	"lr - lowboy or rgn (dat)":          "LR",
	"lo - lowboy overdimension (dat)":   "LO",
	"mv - moving van (dat)":             "MV",
	"nu - pneumatic (dat)":              "NU",
	"po - power only (dat)":             "PO",
	"r - reefer (dat)":                  "R",
	"ra - reefer airride (dat)":         "RA",
	"r2 - reefer doubles (dat)":         "R2",
	"rz - reefer hazmat (dat)":          "RZ",
	"rn - reefer intermodal (dat)":      "RN",
	"rl - reefer logistics (dat)":       "RL",
	"rv - reefer or vented van":         "RV",
	"rx - reefer pallet exchange":       "RX",
	"rp - reefer pallet exchange (dat)": "RP",
	"rm - reefer w/ team (dat)":         "RM",
	"rg - removable gooseneck (dat)":    "RG",
	"sd - step deck (dat)":              "SD",
	"sr - step deck or rgn":             "SR",
	"sn - stepdeck conestoga (dat)":     "SN",
	"sb - straight box truck":           "SB",
	"st - strectch trailer (dat)":       "ST",
	"ta - tanker aluminum (dat)":        "TA",
	"tn - tanker intermodal (dat)":      "TN",
	"ts - tanker steel (dat)":           "TS",
	"tt - truck and trailer (dat)":      "TT",
	"v - van (dat)":                     "V",
	"va - van airride (dat)":            "VA",
	"vw - van blanket wrap":             "VW",
	"vs - van conestoga (dat)":          "VS",
	"vc - van curtain (dat)":            "VC",
	"v2 - van double (dat)":             "V2",
	"vz - van hazmat (dat)":             "VZ",
	"vh - van hotshot (dat)":            "VH",
	"vi - van insulated (dat)":          "VI",
	"vn - van intermodal (dat)":         "VN",
	"vg - van lift-gate (dat)":          "VG",
	"vl - van logistics (dat)":          "VL",
	"ot - van opentop (dat)":            "OT",
	"vf - van or flatbed (dat)":         "VF",
	"vt - van or flats w/tarps (dat)":   "VT",
	"vr - van or reefer (dat)":          "VR",
	"vp - van pallet exchange (dat)":    "VP",
	"vb - van roller bed (dat)":         "VB",
	"vv - van vented (dat)":             "VV",
	"vm - van w/ team (dat)":            "VM",
}

var tumaloTrailerCodeToLabelMap = map[string]string{
	"VR53": "vr53 - 53 foot dry van or reefer",
	"V53":  "v53 - 53' van",
	"ACP":  "acp - air - cargo plane",
	"AC":   "ac - auto carrier (dat)",
	"CN":   "cn - conestoga (dat)",
	"C":    "c - container (dat)",
	"CR":   "cr - container refrigerated (dat)",
	"CI":   "ci - container, insulated (dat)",
	"CV":   "cv - conveyor (dat)",
	"DD":   "dd - double drop (dat)",
	"LA":   "la - drop deck, landoll (dat)",
	"DT":   "dt - dump trailer (dat)",
	"FR":   "fr - flat/van/reefer (dat)",
	"F":    "f - flatbed (dat)",
	"FA":   "fa - flatbed airride (dat)",
	"BT":   "bt - flatbed b-train (dat)",
	"FN":   "fn - flatbed conestoga (dat)",
	"FZ":   "fz - flatbed hazmat (dat)",
	"FH":   "fh - flatbed hotshot (dat)",
	"MX":   "mx - flatbed maxi (dat)",
	"FD":   "fd - flatbed or step deck (dat)",
	"FO":   "fo - flatbed over-dimension",
	"FM":   "fm - flatbed w/ team (dat)",
	"FS":   "fs - flatbed w/sides",
	"FT":   "ft - flatbed w/tarps (dat)",
	"FC":   "fc - flatbed with chains (dat)",
	"HB":   "hb - hopper bottom (dat)",
	"LB":   "lb - lowboy (dat)",
	"LR":   "lr - lowboy or rgn (dat)",
	"LO":   "lo - lowboy overdimension (dat)",
	"MV":   "mv - moving van (dat)",
	"NU":   "nu - pneumatic (dat)",
	"PO":   "po - power only (dat)",
	"R":    "r - reefer (dat)",
	"RA":   "ra - reefer airride (dat)",
	"R2":   "r2 - reefer doubles (dat)",
	"RZ":   "rz - reefer hazmat (dat)",
	"RN":   "rn - reefer intermodal (dat)",
	"RL":   "rl - reefer logistics (dat)",
	"RV":   "rv - reefer or vented van",
	"RX":   "rx - reefer pallet exchange",
	"RP":   "rp - reefer pallet exchange (dat)",
	"RM":   "rm - reefer w/ team (dat)",
	"RG":   "rg - removable gooseneck (dat)",
	"SD":   "sd - step deck (dat)",
	"SR":   "sr - step deck or rgn",
	"SN":   "sn - stepdeck conestoga (dat)",
	"SB":   "sb - straight box truck",
	"ST":   "st - strectch trailer (dat)",
	"TA":   "ta - tanker aluminum (dat)",
	"TN":   "tn - tanker intermodal (dat)",
	"TS":   "ts - tanker steel (dat)",
	"TT":   "tt - truck and trailer (dat)",
	"V":    "v - van (dat)",
	"VA":   "va - van airride (dat)",
	"VW":   "vw - van blanket wrap",
	"VS":   "vs - van conestoga (dat)",
	"VC":   "vc - van curtain (dat)",
	"V2":   "v2 - van double (dat)",
	"VZ":   "vz - van hazmat (dat)",
	"VH":   "vh - van hotshot (dat)",
	"VI":   "vi - van insulated (dat)",
	"VN":   "vn - van intermodal (dat)",
	"VG":   "vg - van lift-gate (dat)",
	"VL":   "vl - van logistics (dat)",
	"OT":   "ot - van opentop (dat)",
	"VF":   "vf - van or flatbed (dat)",
	"VT":   "vt - van or flats w/tarps (dat)",
	"VR":   "vr - van or reefer (dat)",
	"VP":   "vp - van pallet exchange (dat)",
	"VB":   "vb - van roller bed (dat)",
	"VV":   "vv - van vented (dat)",
	"VM":   "vm - van w/ team (dat)",
}

func mapTridentTransportTypeEnum(transportType string) (models.TransportType, error) {
	transportType = strings.TrimSpace(strings.ToLower(transportType))

	if transportType, exists := tridentTransportTypeEnumMap[transportType]; exists {
		return transportType, nil
	}

	return "", fmt.Errorf("unsupported transport type: %s", transportType)
}

func mapFetchTransportTypeEnum(transportType string) (models.TransportType, error) {
	transportType = strings.TrimSpace(strings.ToLower(transportType))

	if transportType, exists := fetchFreightTransportTypeEnumMap[transportType]; exists {
		return transportType, nil
	}

	return "", nil
}

var tridentTransportTypeEnumMap = map[string]models.TransportType{
	"":    "",
	"26b": models.BoxTruckTransportType,
	"26'": models.BoxTruckTransportType,

	"53f":                          models.FlatbedTransportType,
	"53ft flatbed":                 models.FlatbedTransportType,
	"ac":                           models.SpecialTransportType,
	"auto carrier":                 models.SpecialTransportType,
	"b":                            models.SpecialTransportType,
	"beam trailer":                 models.SpecialTransportType,
	"cn":                           models.FlatbedTransportType,
	"conestoga (dat)":              models.FlatbedTransportType,
	"c":                            "",
	"container":                    "",
	"cr":                           models.ReeferTransportType,
	"container refrigerated (dat)": models.ReeferTransportType,
	"ci":                           models.ReeferTransportType,
	"container, insulated (dat)":   models.ReeferTransportType,

	"cv":                            models.FlatbedTransportType,
	"conveyor (dat)":                models.FlatbedTransportType,
	"dd":                            models.FlatbedTransportType,
	"double drop (dat)":             models.FlatbedTransportType,
	"ddt":                           "DDT",
	"drop deck trailers (dat)":      models.FlatbedTransportType,
	"dl":                            models.FlatbedTransportType,
	"drop deck, landoll (dat)":      models.FlatbedTransportType,
	"dt":                            models.FlatbedTransportType,
	"dump trailer (dat)":            models.FlatbedTransportType,
	"fr":                            models.VanTransportType,
	"flat/van/reefer (dat)":         models.VanTransportType,
	"f":                             models.FlatbedTransportType,
	"flatbed (dat)":                 models.FlatbedTransportType,
	"fa":                            models.FlatbedTransportType,
	"flatbed airride (dat)":         models.FlatbedTransportType,
	"bt":                            models.FlatbedTransportType,
	"flatbed b-train (dat)":         models.FlatbedTransportType,
	"fn":                            models.FlatbedTransportType,
	"flatbed conestoga (dat)":       models.FlatbedTransportType,
	"fz":                            models.FlatbedTransportType,
	"flatbed hazmat (dat)":          models.FlatbedTransportType,
	"fh":                            models.FlatbedTransportType,
	"flatbed hotshot (dat)":         models.FlatbedTransportType,
	"mx":                            models.FlatbedTransportType,
	"flatbed maxi (dat)":            models.FlatbedTransportType,
	"fmo":                           models.FlatbedTransportType,
	"flatbed moffett (dat)":         models.FlatbedTransportType,
	"fd":                            models.FlatbedTransportType,
	"flatbed or step deck (dat)":    models.FlatbedTransportType,
	"fo":                            models.FlatbedTransportType,
	"flatbed over-dimension (dat)":  models.FlatbedTransportType,
	"fte":                           models.FlatbedTransportType,
	"flatbed tanker endorsed (dat)": models.FlatbedTransportType,
	"fm":                            models.FlatbedTransportType,
	"flatbed w/ team (dat)":         models.FlatbedTransportType,
	"ft":                            models.FlatbedTransportType,
	"flatbed w/tarps (dat)":         models.FlatbedTransportType,
	"fc":                            models.FlatbedTransportType,
	"flatbed with chains (dat)":     models.FlatbedTransportType,

	"log":                           models.FlatbedTransportType,
	"forestry trailers":             models.FlatbedTransportType,
	"hb":                            models.FlatbedTransportType,
	"hopper bottom (dat)":           models.FlatbedTransportType,
	"ir":                            models.ReeferTransportType,
	"insulated van or reefer (dat)": models.ReeferTransportType,

	"lb":                         models.FlatbedTransportType,
	"lowboy (dat)":               models.FlatbedTransportType,
	"lr":                         models.FlatbedTransportType,
	"lowboy or rgn (dat)":        models.FlatbedTransportType,
	"lo":                         models.FlatbedTransportType,
	"lowboy overdimension (dat)": models.FlatbedTransportType,

	"mv":               models.VanTransportType,
	"moving van (dat)": models.VanTransportType,
	"o":                models.SpecialTransportType,
	"other":            models.SpecialTransportType,
	"nu":               models.SpecialTransportType,
	"pneumatic (dat)":  models.SpecialTransportType,
	"po":               models.SpecialTransportType,
	"power only (dat)": models.SpecialTransportType,

	"r":                                   models.ReeferTransportType,
	"reefer (dat)":                        models.ReeferTransportType,
	"ra":                                  models.ReeferTransportType,
	"reefer airride (dat)":                models.ReeferTransportType,
	"r2":                                  models.ReeferTransportType,
	"reefer doubles (dat)":                models.ReeferTransportType,
	"rz":                                  models.ReeferTransportType,
	"reefer hazmat (dat)":                 models.ReeferTransportType,
	"ri":                                  models.ReeferTransportType,
	"reefer intermodal (dat)":             models.ReeferTransportType,
	"rn":                                  models.ReeferTransportType,
	"reefer logistics (dat)":              models.ReeferTransportType,
	"rv":                                  models.ReeferTransportType,
	"reefer or vented van (dat)":          models.ReeferTransportType,
	"rp":                                  models.ReeferTransportType,
	"reefer pallet exchange (dat)":        models.ReeferTransportType,
	"rte":                                 models.ReeferTransportType,
	"reefer tanker endorsed (dat)":        models.ReeferTransportType,
	"rteh":                                models.ReeferTransportType,
	"reefer tanker endorsed hazmat (dat)": models.ReeferTransportType,
	"rm":                                  models.ReeferTransportType,
	"reefer w/ team (dat)":                models.ReeferTransportType,
	"rg":                                  models.FlatbedTransportType,
	"removable gooseneck (dat)":           models.FlatbedTransportType,

	"spr":      models.SprinterTransportType,
	"sprinter": models.SprinterTransportType,

	"sd":                       models.FlatbedTransportType,
	"step deck (dat)":          models.FlatbedTransportType,
	"sr":                       models.FlatbedTransportType,
	"step deck or rgn (dat)":   models.FlatbedTransportType,
	"sn":                       models.FlatbedTransportType,
	"stepdeck conestoga (dat)": models.FlatbedTransportType,
	"sb":                       models.BoxTruckTransportType,
	"straight box truck":       models.BoxTruckTransportType,
	"sbt":                      models.BoxTruckTransportType,
	"straight box trucks":      models.BoxTruckTransportType,
	"st":                       models.FlatbedTransportType,
	"stretch trailer (dat)":    models.FlatbedTransportType,

	"ta":                      models.SpecialTransportType,
	"tanker aluminum (dat)":   models.SpecialTransportType,
	"tn":                      models.SpecialTransportType,
	"tanker intermodal (dat)": models.SpecialTransportType,
	"ts":                      models.SpecialTransportType,
	"tanker steel (dat)":      models.SpecialTransportType,

	"tt":                      models.VanTransportType,
	"truck and trailer (dat)": models.VanTransportType,
	"v":                       models.VanTransportType,
	"van (dat)":               models.VanTransportType,
	"va":                      models.VanTransportType,
	"van airride (dat)":       models.VanTransportType,
	"vw":                      models.VanTransportType,
	"van blanket wrap":        models.VanTransportType,

	"vs":                   models.FlatbedTransportType,
	"van conestoga (dat)":  models.FlatbedTransportType,
	"vc":                   models.VanTransportType,
	"van curtain (dat)":    models.VanTransportType,
	"v2":                   models.VanTransportType,
	"van double (dat)":     models.VanTransportType,
	"vz":                   models.VanTransportType,
	"van hazmat (dat)":     models.VanTransportType,
	"vh":                   models.HotShotTransportType,
	"van hotshot (dat)":    models.HotShotTransportType,
	"vi":                   models.VanTransportType,
	"van insulated (dat)":  models.VanTransportType,
	"vn":                   models.VanTransportType,
	"van intermodal (dat)": models.VanTransportType,
	"vg":                   models.VanTransportType,
	"van lift-gate (dat)":  models.VanTransportType,
	"vl":                   models.VanTransportType,
	"van logistics (dat)":  models.VanTransportType,
	"ot":                   models.VanTransportType,
	"van opentop (dat)":    models.VanTransportType,

	"vf":                               models.VanTransportType,
	"van or flatbed (dat)":             models.VanTransportType,
	"vt":                               models.VanTransportType,
	"van or flats w/tarps (dat)":       models.VanTransportType,
	"vr":                               models.VanTransportType,
	"van or reefer (dat)":              models.VanTransportType,
	"vp":                               models.VanTransportType,
	"van pallet exchange (dat)":        models.VanTransportType,
	"vb":                               models.VanTransportType,
	"van roller bed (dat)":             models.VanTransportType,
	"vte":                              models.VanTransportType,
	"van tanker endorsed (dat)":        models.VanTransportType,
	"vteh":                             models.VanTransportType,
	"van tanker endorsed hazmat (dat)": models.VanTransportType,
	"vv":                               models.VanTransportType,
	"van vented (dat)":                 models.VanTransportType,
	"vm":                               models.VanTransportType,
	"van w/ team (dat)":                models.VanTransportType,

	"flatbed or stepdeck":        models.FlatbedTransportType,
	"container (dat)":            "",
	"step deck or rgn":           models.FlatbedTransportType,
	"flatbed moffett":            models.FlatbedTransportType,
	"flatbed over-dimension":     models.FlatbedTransportType,
	"flatbed tanker endorsed":    models.FlatbedTransportType,
	"power only load out (dat)":  models.SpecialTransportType,
	"power only towaway (dat)":   models.SpecialTransportType,
	"reefer tanker endorsed":     models.ReeferTransportType,
	"van tanker endorsed":        models.VanTransportType,
	"van tanker endorsed hazmat": models.VanTransportType,
}

var fetchFreightTransportTypeEnumMap = map[string]models.TransportType{
	"ac":                           models.SpecialTransportType,
	"auto carrier (dat)":           models.SpecialTransportType,
	"bt":                           models.FlatbedTransportType,
	"c":                            models.VanTransportType,
	"ci":                           models.ReeferTransportType,
	"cn":                           models.FlatbedTransportType,
	"conestoga (dat)":              models.FlatbedTransportType,
	"container (dat)":              models.VanTransportType,
	"container refrigerated (dat)": models.ReeferTransportType,
	"container insulated (dat)":    models.ReeferTransportType,
	"conveyor (dat)":               models.FlatbedTransportType,
	"cr":                           models.ReeferTransportType,
	"cv":                           models.FlatbedTransportType,
	"dd":                           models.FlatbedTransportType,
	"ddt":                          models.FlatbedTransportType,
	"double drop (dat)":            models.FlatbedTransportType,
	"drop deck trailers":           models.FlatbedTransportType,
	"drop deck,landoll (dat)":      models.FlatbedTransportType,
	"dt":                           models.FlatbedTransportType,
	"dump trailer (dat)":           models.FlatbedTransportType,
	"epo":                          models.FlatbedTransportType,
	"f":                            models.FlatbedTransportType,
	"fa":                           models.FlatbedTransportType,
	"fc":                           models.FlatbedTransportType,
	"fd":                           models.FlatbedTransportType,
	"fh":                           models.FlatbedTransportType,

	"flat/van/reefer (dat)":         models.VanTransportType,
	"flatbed (dat)":                 models.FlatbedTransportType,
	"flatbed airride (dat)":         models.FlatbedTransportType,
	"flatbed b-train (dat)":         models.FlatbedTransportType,
	"flatbed conestoga (dat)":       models.FlatbedTransportType,
	"flatbed hazmat (dat)":          models.FlatbedTransportType,
	"flatbed hotshot (dat)":         models.FlatbedTransportType,
	"flatbed maxi (dat)":            models.FlatbedTransportType,
	"flatbed or step deck (dat)":    models.FlatbedTransportType,
	"flatbed over-dimension":        models.FlatbedTransportType,
	"flatbed w/ team (dat)":         models.FlatbedTransportType,
	"flatbed w/tarps (dat)":         models.FlatbedTransportType,
	"flatbed with chains (dat)":     models.FlatbedTransportType,
	"fm":                            models.FlatbedTransportType,
	"fn":                            models.FlatbedTransportType,
	"fo":                            models.FlatbedTransportType,
	"forestry trailers":             models.FlatbedTransportType,
	"fr":                            models.VanTransportType,
	"ft":                            models.FlatbedTransportType,
	"fz":                            models.FlatbedTransportType,
	"hb":                            models.FlatbedTransportType,
	"hopper bottom (dat)":           models.FlatbedTransportType,
	"im10":                          "", // TODO: Get from CSV
	"im20":                          "",
	"im40":                          "",
	"im45":                          "",
	"im48":                          "",
	"im53":                          "",
	"imdl container 10'":            "",
	"imdl container 20'":            "",
	"imdl container 40'":            "",
	"imdl container 45'":            "",
	"imdl container 48'":            "",
	"imdl container 53'":            "",
	"insulated van or reefer (dat)": models.ReeferTransportType,
	"ir":                            models.ReeferTransportType,
	"la":                            "",
	"lb":                            models.FlatbedTransportType,
	"lo":                            models.FlatbedTransportType,
	"log":                           models.FlatbedTransportType,
	"lowboy (dat)":                  models.FlatbedTransportType,
	"lowboy or rgn (dat)":           models.FlatbedTransportType,
	"lowboy overdimension (dat)":    models.FlatbedTransportType,
	"lr":                            models.FlatbedTransportType,
	"moving van (dat)":              models.VanTransportType,
	"mv":                            models.VanTransportType,
	"mx":                            models.FlatbedTransportType,
	"nu":                            models.SpecialTransportType,
	"o":                             models.SpecialTransportType,
	"ot":                            models.VanTransportType,
	"other":                         models.SpecialTransportType,
	"pneumatic (dat)":               models.SpecialTransportType,
	"power only (dat)":              models.SpecialTransportType,
	"r":                             models.ReeferTransportType,
	"r2":                            models.ReeferTransportType,
	"ra":                            models.ReeferTransportType,
	"reefer (dat)":                  models.ReeferTransportType,
	"reefer airride (dat)":          models.ReeferTransportType,
	"reefer doubles (dat)":          models.ReeferTransportType,
	"reefer hazmat (dat)":           models.ReeferTransportType,
	"reefer intermodal (dat)":       models.ReeferTransportType,
	"reefer logistics (dat)":        models.ReeferTransportType,
	"reefer or vented van":          models.ReeferTransportType,
	"reefer pallet exchange (dat)":  models.ReeferTransportType,
	"reefer w/ team (dat)":          models.ReeferTransportType,
	"removable gooseneck (dat)":     models.FlatbedTransportType,
	"rg":                            models.FlatbedTransportType,
	"rl":                            models.FlatbedTransportType,
	"rm":                            models.ReeferTransportType,
	"rn":                            models.ReeferTransportType,
	"rp":                            models.ReeferTransportType,
	"rv":                            models.ReeferTransportType,
	"rz":                            models.ReeferTransportType,
	"sb":                            models.BoxTruckTransportType,
	"sbt":                           models.BoxTruckTransportType,
	"sd":                            models.FlatbedTransportType,
	"sn":                            models.FlatbedTransportType,
	"sr":                            models.FlatbedTransportType,
	"st":                            models.FlatbedTransportType,
	"step deck (dat)":               models.FlatbedTransportType,
	"step deck or rgn":              models.FlatbedTransportType,
	"stepdeck conestoga (dat)":      models.FlatbedTransportType,
	"straight box truck":            models.BoxTruckTransportType,
	"straight box trucks":           models.BoxTruckTransportType,
	"stretch trailer (dat)":         models.FlatbedTransportType,
	"ta":                            models.SpecialTransportType,
	"tanker aluminum (dat)":         models.SpecialTransportType,
	"tanker intermodal (dat)":       models.SpecialTransportType,
	"tanker steel (dat)":            models.SpecialTransportType,
	"tn":                            models.SpecialTransportType,
	"truck and trailer (dat)":       models.VanTransportType,
	"ts":                            models.SpecialTransportType,
	"tt":                            models.VanTransportType,
	"v":                             models.VanTransportType,
	"v2":                            models.VanTransportType,
	"va":                            models.VanTransportType,
	"van (dat)":                     models.VanTransportType,
	"van airride (dat)":             models.VanTransportType,
	"van blanket wrap":              models.VanTransportType,
	"van conestoga (dat)":           models.FlatbedTransportType,
	"van curtain (dat)":             models.VanTransportType,
	"van double (dat)":              models.VanTransportType,
	"van hazmat (dat)":              models.VanTransportType,
	"van hotshot (dat)":             models.HotShotTransportType,
	"van insulated (dat)":           models.VanTransportType,
	"van intermodal (dat)":          models.VanTransportType,
	"van lift-gate (dat)":           models.VanTransportType,
	"van logistics (dat)":           models.VanTransportType,
	"van ltl":                       models.VanTransportType,
	"van opentop (dat)":             models.VanTransportType,
	"van or flatbed (dat)":          models.VanTransportType,
	"van or flats w/tarps (dat)":    models.VanTransportType,
	"van or reefer (dat)":           models.VanTransportType,
	"van pallet exchange (dat)":     models.VanTransportType,
	"van roller bed (dat)":          models.VanTransportType,
	"van vented (dat)":              models.VanTransportType,
	"van w/ team (dat)":             models.VanTransportType,
	"vb":                            models.VanTransportType,
	"vc":                            models.VanTransportType,
	"vf":                            models.VanTransportType,
	"vg":                            models.VanTransportType,
	"vh":                            models.HotShotTransportType,
	"vi":                            models.VanTransportType,
	"vl":                            models.VanTransportType,
	"vltl":                          models.VanTransportType,
	"vm":                            models.VanTransportType,
	"vn":                            models.VanTransportType,
	"vp":                            models.VanTransportType,
	"vr":                            models.VanTransportType,
	"vs":                            models.FlatbedTransportType,
	"vt":                            models.VanTransportType,
	"vv":                            models.VanTransportType,
	"vw":                            models.VanTransportType,
	"vz":                            models.VanTransportType,
	"flatbed or stepdeck":           models.FlatbedTransportType,
	"flatbed moffett":               models.FlatbedTransportType,
	"flatbed tanker endorsed":       models.FlatbedTransportType,
	"power only load out (dat)":     models.SpecialTransportType,
	"power only towaway (dat)":      models.SpecialTransportType,
	"reefer tanker endorsed":        models.ReeferTransportType,
	"van tanker endorsed":           models.VanTransportType,
	"van tanker endorsed hazmat":    models.VanTransportType,
}
