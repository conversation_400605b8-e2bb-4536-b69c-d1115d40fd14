package featureflags

import (
	"reflect"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
)

// GetValidFeatureFlags returns a list of all valid feature flag JSON field names.
// Exported so it can be used by other packages for validation.
func GetValidFeatureFlags() []string {
	return getValidFeatureFlags()
}

// Merge combines service-level feature flags with user group and user overrides.
// Priority order (highest first):
// 1. User overrides
// 2. User group overrides
// 3. Service flags
func Merge(
	serviceFlags models.FeatureFlags,
	userGroups []models.UserGroup,
	userOverrides models.FeatureFlagOverrides,
) models.FeatureFlags {

	// Start with service flags as the base source. Service flags can't be null.
	merged := serviceFlags

	// Build a lookup map: JSON tag name -> field position in struct
	jsonTagToFieldIndex := buildJSONTagToFieldIndexMap(merged)

	// Priority 1: Apply user group overrides (middle priority)
	applyGroupOverrides(&merged, userGroups, jsonTagToFieldIndex)

	// Priority 2: Apply user-level overrides (highest priority)
	applyUserOverrides(&merged, userOverrides, jsonTagToFieldIndex)

	return merged
}

func getValidFeatureFlags() []string {
	featureFlagsType := reflect.TypeFor[models.FeatureFlags]()
	flags := make([]string, 0, featureFlagsType.NumField())

	for i := 0; i < featureFlagsType.NumField(); i++ {
		field := featureFlagsType.Field(i)
		if field.Type.Kind() == reflect.Bool {
			jsonTag := field.Tag.Get("json")
			if jsonTag != "" && jsonTag != "-" {
				flags = append(flags, jsonTag)
			}
		}
	}

	return flags
}

// FindFieldByJSONName finds a feature flag field by its JSON tag name (case-insensitive).
func FindFieldByJSONName(featureFlags *models.FeatureFlags, jsonName string) (reflect.Value, bool) {
	featureFlagsValue := reflect.ValueOf(featureFlags).Elem()
	featureFlagsType := featureFlagsValue.Type()

	for i := 0; i < featureFlagsType.NumField(); i++ {
		field := featureFlagsType.Field(i)
		jsonTag := field.Tag.Get("json")

		if jsonTag != "" && jsonTag != "-" && strings.EqualFold(jsonTag, jsonName) {
			return featureFlagsValue.Field(i), true
		}
	}

	return reflect.Value{}, false
}

// buildJSONTagToFieldIndexMap creates a map from JSON tag names to their field positions.
// This is needed because overrides are stored as JSON keys (e.g., "isQuickQuoteEnabled"),
// but we need to find the actual struct field to update.
//
// Example result: {"isQuickQuoteEnabled": 4, "isLoadBuildingEnabled": 5, ...}
func buildJSONTagToFieldIndexMap(flags models.FeatureFlags) map[string]int {
	flagsType := reflect.TypeOf(flags)
	result := make(map[string]int, flagsType.NumField())

	for i := 0; i < flagsType.NumField(); i++ {
		field := flagsType.Field(i)
		// Get the JSON tag (e.g., "isQuickQuoteEnabled" from `json:"isQuickQuoteEnabled"`)
		jsonTag := field.Tag.Get("json")

		if jsonTag != "" && jsonTag != "-" && field.Type.Kind() == reflect.Bool {
			// Store the mapping: JSON tag name -> field index
			result[jsonTag] = i
		}
	}

	return result
}

func applyGroupOverrides(
	merged *models.FeatureFlags,
	userGroups []models.UserGroup,
	jsonTagToFieldIndex map[string]int,
) {

	if len(userGroups) == 0 {
		return
	}

	// Track which flags have been already overridden by a group to avoid double-overriding
	alreadyOverridden := make(map[string]bool, len(jsonTagToFieldIndex))
	mergedValue := reflect.ValueOf(merged).Elem()

	for _, group := range userGroups {
		if group.FeatureFlagOverrides == nil {
			continue
		}

		for jsonTagName, overrideValue := range group.FeatureFlagOverrides {
			if overrideValue == nil || alreadyOverridden[strings.ToLower(jsonTagName)] {
				continue
			}

			if applyOverrideToField(mergedValue, jsonTagName, *overrideValue, jsonTagToFieldIndex) {
				alreadyOverridden[strings.ToLower(jsonTagName)] = true
			}
		}
	}
}

func applyUserOverrides(
	merged *models.FeatureFlags,
	userOverrides models.FeatureFlagOverrides,
	jsonTagToFieldIndex map[string]int,
) {

	if userOverrides == nil {
		return
	}

	mergedValue := reflect.ValueOf(merged).Elem()

	for jsonTagName, overrideValue := range userOverrides {
		if overrideValue == nil {
			continue
		}

		applyOverrideToField(mergedValue, jsonTagName, *overrideValue, jsonTagToFieldIndex)
	}
}

func applyOverrideToField(
	structValue reflect.Value,
	jsonTagName string,
	value bool,
	jsonTagToFieldIndex map[string]int,
) bool {

	fieldIndex, exists := jsonTagToFieldIndex[jsonTagName]
	if !exists {
		// Perform case-insensitive lookup for robustness
		lowered := strings.ToLower(jsonTagName)
		for tag, idx := range jsonTagToFieldIndex {
			if strings.ToLower(tag) == lowered {
				fieldIndex = idx
				exists = true
				break
			}
		}
	}

	if !exists {
		return false
	}

	fieldValue := structValue.Field(fieldIndex)
	if !fieldValue.IsValid() || !fieldValue.CanSet() || fieldValue.Kind() != reflect.Bool {
		return false
	}

	fieldValue.SetBool(value)
	return true
}
