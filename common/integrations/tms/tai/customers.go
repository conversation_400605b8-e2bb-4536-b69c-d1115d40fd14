package tai

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/tmsrefresh"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsBrokerStaffDB "github.com/drumkitai/drumkit/common/rds/tms_broker_staff"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/redis"
)

type ExcelCustomerRow struct {
	Name string
}

const (
	customerExportEndpoint           = "/BackOffice/Customer/Export"
	customerByOrganizationIDEndpoint = "/PublicApi/Customer/v2/Customers"

	batchSizeCustomerProcessing = 10
	acceptHeaderCustomerExport  = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet," +
		"application/octet-stream,*/*"
)

// GetCustomers fetches customers from the Tai TMS.
// Tai API exposes customer data via an Excel export; we download and parse that file
// directly in-memory, which is sufficient since the file is simple and rather small (> 300kb).
func (t Tai) GetCustomers(
	ctx context.Context,
	opts ...models.TMSOption,
) (customers []models.TMSCustomer, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomersTai", otel.IntegrationAttrs(t.tms))
	defer func() { metaSpan.End(err) }()

	options := &models.TMSOptions{}
	options.Apply(opts...)

	jobType := redis.CustomerJob
	if options.IsPollerJob {
		jobType = redis.CustomerRefreshJob
	}

	// Tai has no last-updated data; cursor is the index into the Excel list.
	cursor, _ := tmsrefresh.GetRefreshState(
		ctx,
		t.tms.ID,
		jobType,
		options,
		integrationDB.LastCustomerUpdatedAt,
		time.RFC3339,
	)
	startIndex := 0
	if cursor != "" {
		if n, parseErr := strconv.Atoi(cursor); parseErr == nil && n >= 0 {
			startIndex = n
			log.Info(ctx, "starting Tai customer sync from index", zap.Int("startIndex", startIndex))
		} else {
			log.Warn(
				ctx,
				"invalid cursor format, starting from beginning",
				zap.String("cursor", cursor),
			)
		}
	}

	log.Info(ctx, "starting Tai customer sync")

	webClient, err := t.GetAuthenticatedWebClient(ctx)
	if err != nil {
		log.Error(ctx, "failed to authenticate for customer sync", zap.Error(err))
		return nil, fmt.Errorf("Tai authentication failed: %w", err)
	}

	excelData, err := t.fetchCustomerExport(ctx, webClient)
	if err != nil {
		log.Error(ctx, "failed to fetch Tai customer export", zap.Error(err))
		return nil, err
	}

	log.Info(ctx, "parsing Excel file in memory", zap.Int("sizeBytes", len(excelData)))

	excelCustomers, err := readCustomersFromExcel(ctx, excelData)
	if err != nil {
		err = fmt.Errorf("failed to parse Excel file: %w", err)
		log.Error(ctx, "failed to parse Excel file", zap.Error(err))
		return nil, err
	}

	totalCustomersInExcel := len(excelCustomers)
	log.Info(ctx, "parsed customers from Excel", zap.Int("totalCustomers", totalCustomersInExcel))

	if startIndex >= len(excelCustomers) {
		log.Info(ctx, "cursor past end of list, starting from beginning", zap.Int("startIndex", startIndex))
		startIndex = 0
	}

	var allCustomers []models.TMSCustomer
	var failedCount int

	// Process customers in batches; cursor = next index so poller can resume on timeout/failure.
	for batchStart := startIndex; batchStart < len(excelCustomers); batchStart += batchSizeCustomerProcessing {
		batchEnd := batchStart + batchSizeCustomerProcessing
		if batchEnd > len(excelCustomers) {
			batchEnd = len(excelCustomers)
		}

		log.Info(
			ctx,
			"processing customer batch",
			zap.Int("batchStart", batchStart+1),
			zap.Int("batchEnd", batchEnd),
			zap.Int("total", len(excelCustomers)),
		)

		batchCustomers := excelCustomers[batchStart:batchEnd]
		var tmsCustomers []models.TMSCustomer

		for _, excelCustomer := range batchCustomers {

			searchResults, searchErr := t.searchCustomersWithClient(ctx, webClient, excelCustomer.Name)
			if searchErr != nil || len(searchResults) == 0 {
				log.Warn(
					ctx,
					"failed to search for customer",
					zap.String("customerName", excelCustomer.Name),
					zap.Error(searchErr),
				)
				if strings.Contains(searchErr.Error(), "429") {
					return nil, fmt.Errorf("rate limit exceeded: %w", searchErr)
				}

				failedCount++
				time.Sleep(2 * time.Second)
				continue
			}
			orgID := searchResults[0].ID

			customerResp, fetchErr := t.GetCustomerByOrganizationID(ctx, orgID)
			if fetchErr != nil {
				log.Warn(
					ctx,
					"failed to fetch customer details",
					zap.Int("organizationID", orgID),
					zap.String("customerName", excelCustomer.Name),
					zap.Error(fetchErr),
				)
				if strings.Contains(fetchErr.Error(), "429") {
					return nil, fmt.Errorf("rate limit exceeded: %w", fetchErr)
				}

				failedCount++
				time.Sleep(2 * time.Second)
				continue
			}

			// Add delay to avoid rate limiting
			time.Sleep(2 * time.Second)

			tmsCustomers = append(tmsCustomers, t.TaiCustomerToTMSCustomer(customerResp))

			// Customers in Tai have associated broker staff, so we attempt to fetch and persist those as well.
			staff, staffErr := t.LoadBrokerStaff(ctx, orgID)
			if staffErr != nil {
				log.Warn(
					ctx,
					"failed to fetch broker staff for customer",
					zap.Int("organizationID", orgID),
					zap.String("customerName", excelCustomer.Name),
					zap.Error(staffErr),
				)
			} else if len(staff) > 0 {
				// If broker staff is found, save it to the database immediately (non-batched)
				if staffRefreshErr := tmsBrokerStaffDB.RefreshTMSBrokerStaff(ctx, &staff); staffRefreshErr != nil {
					log.Warn(
						ctx,
						"failed to save broker staff for customer",
						zap.Int("organizationID", orgID),
						zap.String("customerName", excelCustomer.Name),
						zap.Error(staffRefreshErr),
					)
				}
			}
		}

		if len(tmsCustomers) > 0 {
			if err = tmsCustomerDB.RefreshTMSCustomers(ctx, &tmsCustomers); err != nil {
				log.Error(
					ctx,
					"failed to save customer batch to database",
					zap.Int("batchSize", len(tmsCustomers)),
					zap.Error(err),
				)

				return allCustomers, fmt.Errorf("failed to save customer batch to database: %w", err)
			}

			log.Info(
				ctx,
				"successfully saved customer batch",
				zap.Int("batchSize", len(tmsCustomers)),
				zap.Int("totalSaved", len(allCustomers)+len(tmsCustomers)),
			)

			allCustomers = append(allCustomers, tmsCustomers...)
		}

		if options.OnProgress != nil {
			options.OnProgress("", strconv.Itoa(batchEnd))
		}
	}

	log.Info(
		ctx,
		"Tai customer sync completed",
		zap.Int("totalInExcel", totalCustomersInExcel),
		zap.Int("successfullyFetched", len(allCustomers)),
		zap.Int("failed", failedCount),
	)

	return allCustomers, nil
}

func (t Tai) GetCustomerByOrganizationID(ctx context.Context, orgID int) (*CustomerResp, error) {
	q := url.Values{}
	q.Set("organizationId", strconv.Itoa(orgID))

	var customer CustomerResp
	err := t.get(ctx, customerByOrganizationIDEndpoint, q, &customer, s3backup.TypeCustomers)
	if err != nil {
		return nil, fmt.Errorf("failed to get customer by organization ID: %w", err)
	}

	return &customer, nil
}

// fetchCustomerExport downloads the latest Tai customer export and returns the Excel bytes.
func (t Tai) fetchCustomerExport(ctx context.Context, webClient *http.Client) ([]byte, error) {
	values := url.Values{}
	values.Set("isFactoringCompany", "false")
	values.Set("enabled", "1")
	values.Set("noCreditLimitSet", "false")
	values.Set("overCreditLimitOnly", "false")

	exportURL := url.URL{
		Scheme:   "https",
		Host:     t.host,
		Path:     customerExportEndpoint,
		RawQuery: values.Encode(),
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, exportURL.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create Tai export request: %w", err)
	}

	req.Header.Set("Accept", acceptHeaderCustomerExport)

	log.Info(ctx, "requesting Tai customer export", zap.String("url", exportURL.String()))

	resp, err := webClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("Tai customer export request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, readErr := io.ReadAll(io.LimitReader(resp.Body, 4096))
		if readErr != nil {
			return nil, fmt.Errorf(
				"Tai customer export returned status %d (body read failed: %w)",
				resp.StatusCode,
				readErr,
			)
		}

		return nil, fmt.Errorf(
			"Tai customer export returned status %d: %s",
			resp.StatusCode,
			string(body),
		)
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read Tai customer export response: %w", err)
	}

	if len(data) == 0 {
		return nil, errors.New("received empty Tai customer export file")
	}

	return data, nil
}

func readCustomersFromExcel(ctx context.Context, data []byte) ([]ExcelCustomerRow, error) {
	log.Info(ctx, "parsing customers from Excel bytes", zap.Int("sizeBytes", len(data)))

	f, err := excelize.OpenReader(bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel from bytes: %w", err)
	}

	defer func() {
		if err := f.Close(); err != nil {
			log.Warn(ctx, "failed to close Excel file", zap.Error(err))
		}
	}()

	return parseExcelCustomerSheet(ctx, f)
}

func parseExcelCustomerSheet(ctx context.Context, f *excelize.File) ([]ExcelCustomerRow, error) {
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return nil, errors.New("no sheets found in Excel file")
	}

	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("failed to get rows from sheet: %w", err)
	}

	if len(rows) == 0 {
		return nil, errors.New("no rows found in Excel file")
	}

	header := rows[0] // Column names
	nameColIdx := -1

	for idx, colName := range header {
		if colName == "name" || colName == "Name" || colName == "NAME" {
			nameColIdx = idx
			break
		}
	}

	if nameColIdx == -1 {
		return nil, errors.New("'name' column not found in Excel file")
	}

	var customers []ExcelCustomerRow
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		if nameColIdx >= len(row) || row[nameColIdx] == "" {
			continue
		}

		customers = append(customers, ExcelCustomerRow{Name: row[nameColIdx]})
	}

	log.Info(ctx, "parsed customers from Excel", zap.Int("count", len(customers)))

	return customers, nil
}

func (t Tai) TaiCustomerToTMSCustomer(customer *CustomerResp) models.TMSCustomer {
	var contactName string
	if customer.Address.ContactName != nil {
		contactName = *customer.Address.ContactName
	}

	var webAddress string
	if customer.WebAddress != nil {
		webAddress = *customer.WebAddress
	}

	tmsCustomer := models.TMSCustomer{
		TMSIntegrationID: t.tms.ID,
		ExternalID:       strconv.Itoa(customer.OrganizationID),
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: strconv.Itoa(customer.OrganizationID),
			Name:          customer.Name,
			AddressLine1:  customer.Address.StreetAddress,
			AddressLine2:  customer.Address.StreetAddressTwo,
			City:          customer.Address.City,
			State:         customer.Address.State,
			Zipcode:       customer.Address.ZipCode,
			Country:       customer.Address.Country,
			Contact:       contactName,
			Phone:         customer.Phone,
			Email:         webAddress,
		},
	}

	fullAddress := models.ConcatAddress(tmsCustomer.CompanyCoreInfo)
	tmsCustomer.NameAddress = models.ConcatNameAddress(tmsCustomer.Name, fullAddress)

	return tmsCustomer
}
