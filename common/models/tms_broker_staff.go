package models

import "gorm.io/gorm"

// NOTE: If you add a new field, you must add it to the RefreshTMSBrokerStaff upsert clause in
// rds/tms_broker_staff/create.go.
type TMSBrokerStaff struct {
	gorm.Model
	TMSIntegrationID uint        `json:"tmsIntegrationId,omitempty"`
	TMSIntegration   Integration `gorm:"foreignKey:TMSIntegrationID" json:"-"`

	ExternalTMSID  string `json:"externalTMSID"`
	OrganizationID int    `json:"organizationId"`

	Login       string `json:"login,omitempty"`
	ContactName string `json:"contactName,omitempty"`
	Email       string `json:"email,omitempty"`
	Phone       string `json:"phone,omitempty"`
	Mobile      string `json:"mobile,omitempty"`
	Fax         string `json:"fax,omitempty"`
	Title       string `json:"title,omitempty"`
	Enabled     bool   `json:"enabled"`

	AddressLine1    string `json:"addressLine1,omitempty"`
	AddressLine2    string `json:"addressLine2,omitempty"`
	City            string `json:"city,omitempty"`
	State           string `json:"state,omitempty"`
	Zipcode         string `json:"zipCode,omitempty"`
	Country         string `json:"country,omitempty"`
	ReferenceNumber string `json:"referenceNumber,omitempty"`
}
