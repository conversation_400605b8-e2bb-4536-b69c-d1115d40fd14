package tai

import (
	"github.com/drumkitai/drumkit/common/models"
)

type (
	SaveLTLQuickQuoteInputRequest struct {
		Request                LTLQuickQuoteRequest
		CustomerOrganizationID int
		CustomerName           string
		CustomerStaffName      string
		BrokerOrganizationName string
		Pickup                 models.Stop
		Dropoff                models.Stop
		EncryptedQuotes        string
	}

	SaveLTLQuickQuoteResult struct {
		QuoteURL    string
		QuoteID     int
		QuoteSource models.QuoteSource
		QuoteLabel  string
	}

	assignmentStatus struct {
		IsAssignedByCurrentUser  bool `json:"isAssignedByCurrentUser"`
		IsAssignedByTeam         bool `json:"isAssignedByTeam"`
		IsCompletedByCurrentUser bool `json:"isCompletedByCurrentUser"`
		IsCompletedByTeam        bool `json:"isCompletedByTeam"`
		IsScheduledByCurrentUser bool `json:"isScheduledByCurrentUser"`
		IsScheduledByTeam        bool `json:"isScheduledByTeam"`
	}

	referenceModel struct {
		DisplayInBackOffice  bool  `json:"displayInBackOffice"`
		DisplayInFrontOffice bool  `json:"displayInFrontOffice"`
		FreeFormAllowed      bool  `json:"freeFormAllowed"`
		HierarchyDepth       int   `json:"hierarchyDepth"`
		IsCustomer           bool  `json:"isCustomer"`
		NoRecord             bool  `json:"noRecord"`
		OrganizationID       int   `json:"organizationId"`
		ReferenceNumbers     []any `json:"referenceNumbers"`
		ReferenceType        int   `json:"referenceType"`
		RequireSelection     bool  `json:"requireSelection"`
		Required             bool  `json:"required"`
	}

	shipmentDomesticCustomerStaff struct {
		Address                 map[string]any `json:"address"`
		BrokerOrganizationID    int            `json:"brokerOrganizationId"`
		BrokerOrganizationName  string         `json:"brokerOrganizationName"`
		BrokerOrganizationPhone string         `json:"brokerOrganizationPhoneNumber"`
		CompanyName             string         `json:"companyName"`
		ContactName             string         `json:"contactName"`
		DimensionsRequired      bool           `json:"dimensionsRequired"`
		NmfcRequired            bool           `json:"nmfcRequired"`
		OrganizationID          int            `json:"organizationId"`
		StaffID                 int            `json:"staffId"`
	}

	shipmentLocationAddress struct {
		City    string `json:"city"`
		Country string `json:"country"`
		State   string `json:"state"`
		ZipCode string `json:"zipCode"`
	}

	shipmentStopReferenceDisplay struct {
		DisplayName string `json:"displayName"`
		Type        int    `json:"type"`
		Value       any    `json:"value"`
	}

	shipmentLocation struct {
		Address                       shipmentLocationAddress        `json:"address"`
		CustomShipmentStopReferences  []any                          `json:"customShipmentStopReferenceNumbers"`
		ScheduledCloseDateTime        *string                        `json:"scheduledCloseDateTime,omitempty"`
		ScheduledReadyDateTime        *string                        `json:"scheduledReadyDateTime,omitempty"`
		ShipmentCommodities           []any                          `json:"shipmentCommodities"`
		ShipmentID                    int                            `json:"shipmentId"`
		ShipmentStopID                int                            `json:"shipmentStopId"`
		ShipmentStopReferenceDisplays []shipmentStopReferenceDisplay `json:"shipmentStopReferenceDisplays"`
		StopType                      int                            `json:"stopType"`
		StopTypeDisplayName           string                         `json:"stopTypeDisplayName"`
		TsaCompliant                  bool                           `json:"tsaCompliant"`
	}

	shipmentCommodity struct {
		PackageType                      int      `json:"packageType"`
		CubicFeet                        string   `json:"cubicFeet"`
		CubicMeters                      string   `json:"cubicMeters"`
		PoundsPerCubicFeet               string   `json:"poundsPerCubicFeet"`
		DimensionalWeight                *int     `json:"dimensionalWeight"`
		ChargeableWeight                 *int     `json:"chargeableWeight"`
		EstimatedFreightClass            string   `json:"estimatedFreightClass"`
		ShowEstimatedFreightClassMessage bool     `json:"showEstimatedFreightClassMessage"`
		CubicFeetChanged                 bool     `json:"cubicFeetChanged"`
		QuantityOfGoods                  int      `json:"quantityOfGoods"`
		TotalWeight                      int      `json:"totalWeight"`
		CommodityDescription             string   `json:"commodityDescription"`
		SizeLength                       *float32 `json:"sizeLength"`
		SizeWidth                        *float32 `json:"sizeWidth"`
		SizeHeight                       *float32 `json:"sizeHeight"`
		FreightClass                     string   `json:"freightClass"`
		NMFCNumber                       string   `json:"nmfcNumber"`
	}

	transitLeg struct {
		SpotQuoteTransitLegOrganizations []any  `json:"spotQuoteTransitLegOrganizations"`
		SpotQuoteTransitReferenceNumbers []any  `json:"spotQuoteTransitReferenceNumbers"`
		TariffProfileName                string `json:"tariffProfileName"`
		TransitType                      int    `json:"transitType"`
		TransitInfo                      any    `json:"transitInfo"`
		DisplayClass                     string `json:"displayClass"`
		ServiceLevel                     int    `json:"serviceLevel"`
		AddedViaRating                   bool   `json:"addedViaRating"`
		BillTo                           any    `json:"billTo"`
		BillExists                       bool   `json:"billExists"`
		InvoiceExists                    bool   `json:"invoiceExists"`
		Status                           int    `json:"status"`
	}

	shipmentDomesticRequest struct {
		Accessorials                        []accessorial                 `json:"accessorials"`
		APIOrEDI204DispatchingAvailable     bool                          `json:"apiOrEDI204DispatchingAvailable"`
		AssignmentStatus                    assignmentStatus              `json:"assignmentStatus"`
		AutoCalculateLinearFeet             bool                          `json:"autoCalculateLinearFeet"`
		BillToOrganizationID                int                           `json:"billToOrganizationId"`
		Billed                              bool                          `json:"billed"`
		BrokerID                            int                           `json:"brokerId"`
		BrokerOrganizationName              string                        `json:"brokerOrganizationName"`
		CreatedDate                         string                        `json:"createdDate"`
		CustomerDefaultShipmentStatus       int                           `json:"customerDefaultShipmentStatus"`
		CustomerOverCreditLimit             bool                          `json:"customerOverCreditLimit"`
		CustomerPONumberModel               map[string]referenceModel     `json:"customerPONumberModel"`
		CustomerStaff                       shipmentDomesticCustomerStaff `json:"customerStaff"`
		CustomerStaffID                     int                           `json:"customerStaffId"`
		DeliveryLocation                    shipmentLocation              `json:"deliveryLocation"`
		DeliveryReadyDateTime               *string                       `json:"deliveryReadyDateTime"`
		DimensionUnit                       int                           `json:"dimensionUnit"`
		DimensionUnitDisplayName            string                        `json:"dimensionUnitDisplayName"`
		DimensionsRequired                  bool                          `json:"dimensionsRequired"`
		DisplayCarrierConfirmation          bool                          `json:"displayCarrierConfirmation"`
		DisplayCarrierConfirmationTONU      bool                          `json:"displayCarrierConfirmationTONU"`
		DisplayableCarrierConfirmationTypes []any                         `json:"displayableCarrierConfirmationTypes"`
		EncryptedQuotes                     string                        `json:"encryptedQuotes"`
		EquipmentLength                     int                           `json:"equipmentLength"`
		EquipmentTypeDisplayName            string                        `json:"equipmentTypeDisplayName"`
		Guaranteed                          bool                          `json:"guaranteed"`
		Invoiced                            bool                          `json:"invoiced"`
		IsDuplicateShipment                 bool                          `json:"isDuplicateShipment"`
		IsHazmat                            bool                          `json:"isHazmat"`
		IsOceanShipment                     bool                          `json:"isOceanShipment"`
		IsVolume                            bool                          `json:"isVolume"`
		LinearFeet                          *int                          `json:"linearFeet"`
		LinearFeetCannotProcessMessage      *string                       `json:"linearFeetCannotProcessMessage"`
		LinearFeetErrorMessage              string                        `json:"linearFeetErrorMessage"`
		LinearFeetWarningMessage            *string                       `json:"linearFeetWarningMessage"`
		Mileage                             float64                       `json:"mileage"`
		ModifiedDate                        string                        `json:"modifiedDate"`
		NmfcRequired                        bool                          `json:"nmfcRequired"`
		NmfcRequiredValidate                bool                          `json:"nmfcRequiredValidate"`
		NoAccountingInvoice                 bool                          `json:"noAccountingInvoice"`
		OverlengthApplied                   bool                          `json:"overlengthApplied"`
		PayerOrganizationID                 int                           `json:"payerOrganizationId"`
		PickupCloseDate                     string                        `json:"pickupCloseDate"`
		PickupCloseDateTime                 string                        `json:"pickupCloseDateTime"`
		PickupCloseTime                     string                        `json:"pickupCloseTime"`
		PickupDateTimeHasPassed             bool                          `json:"pickupDateTimeHasPassed"`
		PickupLocation                      shipmentLocation              `json:"pickupLocation"`
		PickupReadyDate                     string                        `json:"pickupReadyDate"`
		PickupReadyDateTime                 string                        `json:"pickupReadyDateTime"`
		PickupReadyTime                     string                        `json:"pickupReadyTime"`
		PickupTimeAfterCutoffTime           bool                          `json:"pickupTimeAfterCutoffTime"`
		PredictRatesCompleted               bool                          `json:"predictRatesCompleted"`
		RateSelected                        bool                          `json:"rateSelected"`
		RecalculateCommissions              bool                          `json:"recalculateCommissions"`
		RequiredLinearFeetParameters        []string                      `json:"requiredLinearFeetParameters"`
		SalesReps                           []any                         `json:"salesReps"`
		ShipmentAlertTypes                  []any                         `json:"shipmentAlertTypes"`
		ShipmentCommodities                 []shipmentCommodity           `json:"shipmentCommodities"`
		ShipmentID                          int                           `json:"shipmentId"`
		ShipmentStops                       []any                         `json:"shipmentStops"`
		ShipmentType                        int                           `json:"shipmentType"`
		ShipmentTypeDisplayName             string                        `json:"shipmentTypeDisplayName"`
		ShipperReferenceModel               map[string]referenceModel     `json:"shipperReferenceModel"`
		Stackable                           bool                          `json:"stackable"`
		Status                              int                           `json:"status"`
		StatusDisplayName                   any                           `json:"statusDisplayName"`
		TotalCubicFeet                      float64                       `json:"totalCubicFeet"`
		TotalHandlingUnits                  int                           `json:"totalHandlingUnits"`
		TotalPCF                            float64                       `json:"totalPCF"`
		TotalPieces                         int                           `json:"totalPieces"`
		TotalWeight                         int                           `json:"totalWeight"`
		TrailerSize                         int                           `json:"trailerSize"`
		TrailerSizeDisplayName              string                        `json:"trailerSizeDisplayName"`
		TrailerType                         int                           `json:"trailerType"`
		TrailerTypeDisplayName              string                        `json:"trailerTypeDisplayName"`
		TransitLegs                         []transitLeg                  `json:"transitLegs"`
		UserHasCustomerAccess               bool                          `json:"userHasCustomerAccess"`
		WeightUnit                          int                           `json:"weightUnit"`
		WeightUnitDisplayName               string                        `json:"weightUnitDisplayName"`
	}

	shipmentDomesticResponse struct {
		ShipmentID                int    `json:"shipmentId"`
		ShipmentStatus            int    `json:"shipmentStatus"`
		ShipmentStatusDisplayName string `json:"shipmentStatusDisplayName"`
	}

	stopTimeFields struct {
		Ready     string
		Close     string
		ApptBegin *string
		ApptEnd   *string
	}

	accessorialCategoryInfo struct {
		AccessorialType            int
		AccessorialDisplayType     int
		AccessorialTypeDisplayName string
	}

	shipmentTotals struct {
		TotalCubicFeet     float64
		TotalHandlingUnits int
		TotalPCF           float64
		TotalPieces        int
		TotalWeight        int
	}
)
