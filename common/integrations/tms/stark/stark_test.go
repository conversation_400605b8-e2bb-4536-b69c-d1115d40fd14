package stark

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func TestNewStark(t *testing.T) {
	ctx := context.Background()

	t.Run("Use first-class fields", func(t *testing.T) {
		tms := models.Integration{
			UserID:         "user123",
			RogersRevision: "rev456",
		}
		client, err := New(ctx, tms)
		assert.NoError(t, err)
		assert.Equal(t, "user123", client.userID)
		assert.Equal(t, "rev456", client.rogersRevision)
	})

	t.Run("Fall back to Note", func(t *testing.T) {
		tms := models.Integration{
			Note: "user_id:user789, rogers_revision:rev012",
		}
		client, err := New(ctx, tms)
		assert.NoError(t, err)
		assert.Equal(t, "user789", client.userID)
		assert.Equal(t, "rev012", client.rogersRevision)
	})

	t.Run("Priority to first-class fields", func(t *testing.T) {
		tms := models.Integration{
			UserID:         "priority-user",
			RogersRevision: "priority-rev",
			Note:           "user_id:ignored-user, rogers_revision:ignored-rev",
		}
		client, err := New(ctx, tms)
		assert.NoError(t, err)
		assert.Equal(t, "priority-user", client.userID)
		assert.Equal(t, "priority-rev", client.rogersRevision)
	})
}
