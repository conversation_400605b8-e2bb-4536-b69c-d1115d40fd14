package quoteprivate

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/pricing"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/redis"
)

type (
	SearchLTLCustomersQuery struct {
		Name string `query:"name"`
	}

	GetLTLCustomersResponse struct {
		CustomerList []models.TMSCustomer `json:"customerList"`
		TMSTenant    string               `json:"tmsTenant"`
	}
)

// Searches for customers in the integration by name.
func SearchLTLCustomers(c *fiber.Ctx) error {
	ctx := log.With(c.UserContext())
	var query SearchLTLCustomersQuery
	if err := api.Parse(c, nil, &query, nil); err != nil {
		log.Error(ctx, "invalid request query", zap.Error(err))
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)
	userID := middleware.UserIDFromContext(c)

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		log.Error(ctx, "failed to get user by id", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if userServiceID != user.ServiceID {
		log.Infof(
			ctx,
			"claims serviceID %d does not match user service ID %d",
			userServiceID,
			user.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	service, err := rds.GetServiceWithPreload(ctx, user.ServiceID)
	if err != nil {
		log.Error(ctx, "failed to get service by id", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var tmsIntegration *models.Integration
	tmsTenant := ""
	// Cache key partitioning uses the TMS integration ID when available so
	// TMS-backed results do not mix with pricing integration fallbacks.
	// A zero value indicates we are in the pricing-only fallback path.
	cacheScopeID := uint(0)

	if service.QuickQuoteConfig != nil && service.QuickQuoteConfig.TMSIntegrationID != nil {
		integration, err := integrationDB.Get(ctx, *service.QuickQuoteConfig.TMSIntegrationID)
		if err != nil {
			log.Warn(ctx, "failed to get TMS integration, falling back to pricing", zap.Error(err))
		} else {
			tmsIntegration = &integration
			tmsTenant = integration.Tenant
			cacheScopeID = integration.ID
		}
	} else {
		log.Warn(ctx, "quick quote config missing TMS integration association, falling back to pricing")
	}

	cacheKey := fmt.Sprintf(
		"ltl-customers-search:%d:%d:%s",
		service.ID,
		cacheScopeID,
		query.Name,
	)

	if query.Name != "" {
		cachedCustomers, found, err := redis.GetKey[[]models.TMSCustomer](ctx, cacheKey)
		if err != nil {
			log.Warn(
				ctx,
				"Error retrieving LTL customers from cache",
				zap.String("cacheKey", cacheKey),
				zap.Error(err),
			)
		} else if found {
			log.Infof(ctx, "Cache hit for LTL customers search", zap.String("cacheKey", cacheKey))

			return c.Status(http.StatusOK).JSON(
				GetLTLCustomersResponse{
					CustomerList: cachedCustomers,
					TMSTenant:    tmsTenant,
				},
			)
		}
	} else {
		log.Debug(ctx, "Empty search query, skipping cache lookup.")
	}

	var customerList []models.TMSCustomer
	if query.Name != "" && tmsIntegration != nil {
		customerList, err = tmsCustomerDB.FuzzySearchByName(
			ctx,
			tmsCustomerDB.SearchCustomersQuery{
				TMSID: tmsIntegration.ID,
				CompanyCoreInfo: models.CompanyCoreInfo{
					Name: query.Name,
				},
			},
		)
		if err != nil {
			log.Error(ctx, "error searching TMS customers in database", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	if query.Name != "" && len(customerList) == 0 {
		pricingIntegrations, err := integrationDB.GetPricingListByServiceID(ctx, service.ID)
		if err != nil {
			log.Error(ctx, "error fetching pricing integration", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		var taiIntegration models.Integration
		for _, integration := range pricingIntegrations {
			if integration.Name == models.TaiPricing {
				taiIntegration = integration
				break
			}
		}

		if taiIntegration.ID == 0 {
			log.Error(ctx, "no Tai pricing integration found for service")
			return c.SendStatus(http.StatusNotFound)
		}

		if tmsTenant == "" {
			tmsTenant = taiIntegration.Tenant
		}

		client, err := pricing.New(ctx, taiIntegration, models.WithUserEmail(user.EmailAddress))
		if err != nil {
			log.Error(ctx, "failed to create pricing client", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		customerList, err = client.SearchCustomers(ctx, query.Name)
		if err != nil {
			log.Error(ctx, "error searching TMS customers in integration", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	if query.Name != "" && len(customerList) > 0 {
		cacheExpiration := 1 * time.Hour
		err := redis.SetKey(ctx, cacheKey, customerList, cacheExpiration)
		if err != nil {
			log.Warn(
				ctx,
				"Failed to cache LTL customers search results",
				zap.String("cacheKey", cacheKey),
				zap.Error(err),
			)
		} else {
			log.Infof(ctx, "Cached LTL customers search results", zap.String("cacheKey", cacheKey))
		}
	}

	return c.Status(http.StatusOK).JSON(
		GetLTLCustomersResponse{
			CustomerList: customerList,
			TMSTenant:    tmsTenant,
		},
	)
}
