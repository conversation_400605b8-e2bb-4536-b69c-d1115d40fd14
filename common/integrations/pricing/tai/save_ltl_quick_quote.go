package tai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const shipmentDomesticEndpoint = "/api/shipmentDomestic"
const storedQuoteEndpoint = "/back-office/shipment/shipment-details"

const (
	defaultDateTimeSentinel    = "0001-01-01T00:00:00"
	defaultDateTimeSentinelUTC = defaultDateTimeSentinel + "Z"
	linearFeetErrorMessage     = "The following is required in order to calculate linear feet: Handling unit"
	taiDateLayout              = "01/02/2006"
	taiTimeLayout              = "15:04"
	taiLegacyDateTimeLayout    = "2006-01-02T15:04:05"
)

// SaveLTLQuickQuote stores LTL rates in Tai as a shipment quote.
func (c *Client) SaveLTLQuickQuote(
	ctx context.Context,
	input SaveLTLQuickQuoteInputRequest,
) (SaveLTLQuickQuoteResult, error) {

	normalizedInput := input
	if normalizedReq, normalizeErr := NormalizeLTLQuickQuoteLocations(ctx, input.Request); normalizeErr != nil {
		log.Error(
			ctx,
			"failed to normalize LTL locations for Tai save",
			zap.Error(normalizeErr),
			zap.String("originCity", input.Request.OriginCity),
			zap.String("originState", input.Request.OriginState),
			zap.String("originZip", input.Request.OriginZip),
			zap.String("destinationCity", input.Request.DestinationCity),
			zap.String("destinationState", input.Request.DestinationState),
			zap.String("destinationZip", input.Request.DestinationZip),
		)
	} else {
		normalizedInput.Request = normalizedReq
		normalizedInput.Pickup.Address.City = normalizedReq.OriginCity
		normalizedInput.Pickup.Address.State = normalizedReq.OriginState
		normalizedInput.Pickup.Address.Zip = normalizedReq.OriginZip
		normalizedInput.Pickup.Address.Country = normalizedReq.OriginCountry
		normalizedInput.Dropoff.Address.City = normalizedReq.DestinationCity
		normalizedInput.Dropoff.Address.State = normalizedReq.DestinationState
		normalizedInput.Dropoff.Address.Zip = normalizedReq.DestinationZip
		normalizedInput.Dropoff.Address.Country = normalizedReq.DestinationCountry
	}

	staff, err := c.getCustomerStaffDetails(normalizedInput)
	if err != nil {
		return SaveLTLQuickQuoteResult{}, fmt.Errorf("failed to build Tai customer staff payload: %w", err)
	}

	accessorials := c.GetStaticCustomerAccessorials()
	accessorialPayload := buildAccessorialPayload(accessorials, normalizedInput.Request.Accessorials)

	pickupFields, dropoffFields := buildStopTimeFields(normalizedInput.Pickup, normalizedInput.Dropoff)

	payload := buildShipmentDomesticRequest(normalizedInput, staff, accessorialPayload, pickupFields, dropoffFields)
	requestBytes, err := json.Marshal(payload)
	if err != nil {
		return SaveLTLQuickQuoteResult{}, fmt.Errorf("failed to marshal Tai shipmentDomestic payload: %w", err)
	}
	body := bytes.NewBuffer(requestBytes)

	var resp shipmentDomesticResponse
	if _, err := c.post(ctx, shipmentDomesticEndpoint, body, &resp); err != nil {
		return SaveLTLQuickQuoteResult{}, fmt.Errorf("failed to post Tai shipmentDomestic payload: %w", err)
	}

	quoteURL := c.buildStoredQuoteURL(fmt.Sprintf("%s/%d", storedQuoteEndpoint, resp.ShipmentID))

	log.Info(
		ctx,
		"stored Tai LTL quote",
		zap.String("storedQuoteURL", quoteURL),
		zap.Int("shipmentId", resp.ShipmentID),
		zap.Int("status", resp.ShipmentStatus),
		zap.String("statusDisplayName", resp.ShipmentStatusDisplayName),
	)

	res := SaveLTLQuickQuoteResult{
		QuoteURL:    quoteURL,
		QuoteID:     resp.ShipmentID,
		QuoteSource: models.TaiSource,
		QuoteLabel:  models.FormatIntegrationName(models.TaiPricing),
	}

	return res, nil
}

func (c *Client) buildStoredQuoteURL(path string) string {
	u := url.URL{
		Scheme: "https",
		Host:   c.Integration.Tenant,
		Path:   path,
	}

	return u.String()
}

func buildAccessorialPayload(accessorials []models.Accessorial, selectedIDs []int) []accessorial {
	if len(accessorials) == 0 {
		return []accessorial{}
	}

	selected := make(map[int]struct{}, len(selectedIDs))
	for _, id := range selectedIDs {
		selected[id] = struct{}{}
	}

	out := make([]accessorial, 0, len(accessorials))
	for _, acc := range accessorials {
		category := accessorialCategory(acc)
		_, selectedFlag := selected[acc.ExternalID]

		out = append(
			out,
			accessorial{
				AccessorialCode:            acc.Code,
				AccessorialDisplayType:     category.AccessorialDisplayType,
				AccessorialID:              acc.ExternalID,
				AccessorialName:            acc.Name,
				AccessorialType:            category.AccessorialType,
				AccessorialTypeDisplayName: category.AccessorialTypeDisplayName,
				Added:                      selectedFlag,
				Buy:                        0,
				BuyModified:                false,
				ChargeLineType:             1,
				ChargeLineTypeDisplayName:  "Accessorial",
				PuertoRico:                 false,
				Selected:                   selectedFlag,
				SelectedNotVisible:         false,
				Sell:                       0,
				SellModified:               false,
			},
		)
	}

	return out
}

func accessorialCategory(acc models.Accessorial) accessorialCategoryInfo {
	category := models.CategoryShipment
	if len(acc.CategoryTags) > 0 {
		category = acc.CategoryTags[0]
	}

	switch category {
	case models.CategoryPickup:
		return accessorialCategoryInfo{
			AccessorialType:            2,
			AccessorialDisplayType:     2,
			AccessorialTypeDisplayName: "Pickup",
		}
	case models.CategoryDelivery:
		return accessorialCategoryInfo{
			AccessorialType:            3,
			AccessorialDisplayType:     2,
			AccessorialTypeDisplayName: "Delivery",
		}
	default:
		return accessorialCategoryInfo{
			AccessorialType:            1,
			AccessorialDisplayType:     3,
			AccessorialTypeDisplayName: "Shipment",
		}
	}
}

func buildShipmentDomesticRequest(
	input SaveLTLQuickQuoteInputRequest,
	staff shipmentDomesticCustomerStaff,
	accessorials []accessorial,
	pickupFields,
	dropoffFields stopTimeFields,
) shipmentDomesticRequest {

	req := input.Request
	commodities := buildShipmentCommodities(req.LineItems)
	weights := summarizeShipmentCommodities(commodities)

	readyTime, readyTimeValid := parseTaiDateTime(pickupFields.Ready)
	closeTime, closeTimeValid := parseTaiDateTime(pickupFields.Close)

	pickupReadyDate := ""
	if readyTimeValid {
		pickupReadyDate = readyTime.Format(taiDateLayout)
	}

	pickupCloseDate := ""
	if closeTimeValid {
		pickupCloseDate = closeTime.Format(taiDateLayout)
	}

	pickupReadyTime := ""
	if readyTimeValid {
		pickupReadyTime = readyTime.Format(taiTimeLayout)
	}

	pickupCloseTime := ""
	if closeTimeValid {
		pickupCloseTime = closeTime.Format(taiTimeLayout)
	}

	pickupDateTimeHasPassed := false
	if readyTimeValid {
		pickupDateTimeHasPassed = readyTime.Before(time.Now())
	}

	var deliveryReady *string
	if strings.TrimSpace(dropoffFields.Ready) != "" {
		deliveryReady = &dropoffFields.Ready
	}

	shipmentTypeDisplayName := shipmentTypeDisplayName(req.ShipmentType)
	weightUnitDisplayName := weightUnitDisplayName(req.WeightUnit)
	dimensionUnitDisplayName := dimensionUnitDisplayName(req.DimensionUnit)
	trailerTypeDisplayName := trailerTypeDisplayName(req.TrailerType)
	if trailerTypeDisplayName == "" {
		trailerTypeDisplayName = "Van"
	}

	return shipmentDomesticRequest{
		Accessorials:                    accessorials,
		APIOrEDI204DispatchingAvailable: false,
		AssignmentStatus: assignmentStatus{
			IsAssignedByCurrentUser:  false,
			IsAssignedByTeam:         false,
			IsCompletedByCurrentUser: false,
			IsCompletedByTeam:        false,
			IsScheduledByCurrentUser: false,
			IsScheduledByTeam:        false,
		},
		AutoCalculateLinearFeet:       true,
		BillToOrganizationID:          input.CustomerOrganizationID,
		Billed:                        false,
		BrokerID:                      req.BrokerID,
		BrokerOrganizationName:        staff.BrokerOrganizationName,
		CreatedDate:                   defaultDateTimeSentinel,
		CustomerDefaultShipmentStatus: 10,
		CustomerOverCreditLimit:       false,
		CustomerPONumberModel: map[string]referenceModel{
			"2": buildReferenceModel(req.BrokerID, 5),
		},
		CustomerStaff:   staff,
		CustomerStaffID: req.CustomerStaffID,
		DeliveryLocation: buildShipmentLocation(
			input.Dropoff,
			2,
			"Last Drop",
			nil,
			nil,
		),
		DeliveryReadyDateTime:               deliveryReady,
		DimensionUnit:                       req.DimensionUnit,
		DimensionUnitDisplayName:            dimensionUnitDisplayName,
		DimensionsRequired:                  staff.DimensionsRequired,
		DisplayCarrierConfirmation:          false,
		DisplayCarrierConfirmationTONU:      false,
		DisplayableCarrierConfirmationTypes: []any{},
		EncryptedQuotes:                     input.EncryptedQuotes,
		EquipmentLength:                     0,
		EquipmentTypeDisplayName:            trailerTypeDisplayName,
		Guaranteed:                          false,
		Invoiced:                            false,
		IsDuplicateShipment:                 false,
		IsHazmat:                            false,
		IsOceanShipment:                     false,
		IsVolume:                            false,
		LinearFeet:                          req.LinearFeet,
		LinearFeetCannotProcessMessage:      nil,
		LinearFeetErrorMessage:              linearFeetErrorMessage,
		LinearFeetWarningMessage:            nil,
		Mileage:                             req.Mileage,
		ModifiedDate:                        defaultDateTimeSentinel,
		NmfcRequired:                        staff.NmfcRequired,
		NmfcRequiredValidate:                staff.NmfcRequired,
		NoAccountingInvoice:                 false,
		OverlengthApplied:                   false,
		PayerOrganizationID:                 input.CustomerOrganizationID,
		PickupCloseDate:                     pickupCloseDate,
		PickupCloseDateTime:                 pickupFields.Close,
		PickupCloseTime:                     pickupCloseTime,
		PickupDateTimeHasPassed:             pickupDateTimeHasPassed,
		PickupLocation: buildShipmentLocation(
			input.Pickup,
			1,
			"First Pickup",
			&pickupFields.Ready,
			&pickupFields.Close,
		),
		PickupReadyDate:           pickupReadyDate,
		PickupReadyDateTime:       pickupFields.Ready,
		PickupReadyTime:           pickupReadyTime,
		PickupTimeAfterCutoffTime: false,
		PredictRatesCompleted:     true,
		RateSelected:              true,
		RecalculateCommissions:    true,
		RequiredLinearFeetParameters: []string{
			"Handling unit",
		},
		SalesReps:               []any{},
		ShipmentAlertTypes:      []any{},
		ShipmentCommodities:     commodities,
		ShipmentID:              0,
		ShipmentStops:           []any{},
		ShipmentType:            req.ShipmentType,
		ShipmentTypeDisplayName: shipmentTypeDisplayName,
		ShipperReferenceModel: map[string]referenceModel{
			"2": buildReferenceModel(req.BrokerID, 3),
		},
		Stackable:              req.Stackable,
		Status:                 10,
		StatusDisplayName:      0,
		TotalCubicFeet:         weights.TotalCubicFeet,
		TotalHandlingUnits:     weights.TotalHandlingUnits,
		TotalPCF:               weights.TotalPCF,
		TotalPieces:            weights.TotalPieces,
		TotalWeight:            weights.TotalWeight,
		TrailerSize:            0,
		TrailerSizeDisplayName: "Full",
		TrailerType:            req.TrailerType,
		TrailerTypeDisplayName: trailerTypeDisplayName,
		TransitLegs:            []transitLeg{},
		UserHasCustomerAccess:  true,
		WeightUnit:             req.WeightUnit,
		WeightUnitDisplayName:  weightUnitDisplayName,
	}
}

func buildShipmentLocation(
	stop models.Stop,
	stopType int,
	stopTypeDisplayName string,
	scheduledReadyDateTime,
	scheduledCloseDateTime *string,
) shipmentLocation {

	address := shipmentLocationAddress{
		City:    strings.ToUpper(stop.Address.City),
		Country: normalizeCountry(stop.Address.Country),
		State:   strings.ToUpper(stop.Address.State),
		ZipCode: stop.Address.Zip,
	}

	return shipmentLocation{
		Address:                       address,
		CustomShipmentStopReferences:  []any{},
		ScheduledReadyDateTime:        scheduledReadyDateTime,
		ScheduledCloseDateTime:        scheduledCloseDateTime,
		ShipmentCommodities:           []any{},
		ShipmentID:                    0,
		ShipmentStopID:                0,
		ShipmentStopReferenceDisplays: buildStopReferenceDisplays(),
		StopType:                      stopType,
		StopTypeDisplayName:           stopTypeDisplayName,
		TsaCompliant:                  false,
	}
}

var defaultStopReferenceDisplays = []shipmentStopReferenceDisplay{
	{DisplayName: "Appointment Confirmation Number", Type: 51, Value: ""},
	{DisplayName: "Assigned Branch", Type: 1056, Value: ""},
	{DisplayName: "Booth Number", Type: 57, Value: ""},
	{DisplayName: "Cancellation Reason", Type: 60, Value: ""},
	{DisplayName: "Carrier Dispatcher Email", Type: 1041, Value: ""},
	{DisplayName: "Carrier Dispatcher Name", Type: 46, Value: ""},
	{DisplayName: "Carrier Dispatcher Phone Number", Type: 47, Value: ""},
	{DisplayName: "Carrier Rep", Type: 50, Value: ""},
	{DisplayName: "Claim Number", Type: 28, Value: ""},
	{DisplayName: "Container Number", Type: 24, Value: ""},
	{DisplayName: "Container Size", Type: 39, Value: ""},
	{DisplayName: "Custom Reference Number", Type: 26, Value: ""},
	{DisplayName: "Custom Reference Number 2", Type: 27, Value: ""},
	{DisplayName: "Custom Reference Number 3", Type: 1059, Value: ""},
	{DisplayName: "Customer PO Number", Type: 5, Value: ""},
	{DisplayName: "Declared Value", Type: 1061, Value: ""},
	{DisplayName: "Decorator", Type: 58, Value: ""},
	{DisplayName: "Delivery Carrier Pro Number", Type: 22, Value: ""},
	{DisplayName: "Driver Cell Phone Number", Type: 11, Value: ""},
	{DisplayName: "Driver Name", Type: 30, Value: ""},
	{DisplayName: "Earliest Return", Type: 38, Value: ""},
	{DisplayName: "Exhibitor Name", Type: 56, Value: ""},
	{DisplayName: "File Number", Type: 29, Value: ""},
	{DisplayName: "Insurance Certificate Id", Type: 49, Value: ""},
	{DisplayName: "Insurance Commodity", Type: 1076, Value: ""},
	{DisplayName: "Insurance Declared Value", Type: 43, Value: ""},
	{DisplayName: "Insurance Deductible", Type: 1077, Value: ""},
	{DisplayName: "Insurance Type", Type: 1078, Value: ""},
	{DisplayName: "Invoice Number", Type: 6, Value: ""},
	{DisplayName: "Last Free Day", Type: 37, Value: ""},
	{DisplayName: "Linehaul Carrier Pro Number", Type: 20, Value: ""},
	{DisplayName: "Linehaul Pickup Number", Type: 1015, Value: ""},
	{DisplayName: "Load Release Number", Type: 4, Value: ""},
	{DisplayName: "MAWB Number", Type: 23, Value: ""},
	{DisplayName: "Max Buy Rate", Type: 1068, Value: ""},
	{DisplayName: "Min Buy Rate", Type: 1067, Value: ""},
	{DisplayName: "Offer Rate", Type: 1060, Value: ""},
	{DisplayName: "On Hold Reason", Type: 1063, Value: ""},
	{DisplayName: "Payment Check Code", Type: 1053, Value: ""},
	{DisplayName: "Payment Reference Number", Type: 40, Value: ""},
	{DisplayName: "Pickup Carrier Pro Number", Type: 21, Value: ""},
	{DisplayName: "Quality Issue", Type: 59, Value: ""},
	{DisplayName: "Quote Due By Date", Type: 8, Value: nil},
	{DisplayName: "Quote Number", Type: 7, Value: ""},
	{DisplayName: "Reference Number", Type: 1, Value: ""},
	{DisplayName: "Relay Charge Id", Type: 1055, Value: ""},
	{DisplayName: "Relay Code Id", Type: 1054, Value: ""},
	{DisplayName: "Seal Number", Type: 25, Value: ""},
	{DisplayName: "Secondary BOL Number", Type: 15, Value: ""},
	{DisplayName: "Secondary Driver Cell Phone Number", Type: 1058, Value: ""},
	{DisplayName: "Secondary Driver Name", Type: 1057, Value: ""},
	{DisplayName: "Shipper Reference Number", Type: 3, Value: ""},
	{DisplayName: "Shipping Line", Type: 35, Value: ""},
	{DisplayName: "Steamship Line", Type: 33, Value: ""},
	{DisplayName: "Temperature", Type: 32, Value: ""},
	{DisplayName: "Tracking Reference Number", Type: 10, Value: ""},
	{DisplayName: "Trailer Number", Type: 9, Value: ""},
	{DisplayName: "Truck Number", Type: 31, Value: ""},
	{DisplayName: "Vessel / Voyage", Type: 34, Value: ""},
}

func buildStopReferenceDisplays() []shipmentStopReferenceDisplay {
	return append([]shipmentStopReferenceDisplay(nil), defaultStopReferenceDisplays...)
}

func buildReferenceModel(organizationID int, referenceType int) referenceModel {
	return referenceModel{
		DisplayInBackOffice:  true,
		DisplayInFrontOffice: true,
		FreeFormAllowed:      true,
		HierarchyDepth:       2,
		IsCustomer:           false,
		NoRecord:             false,
		OrganizationID:       organizationID,
		ReferenceNumbers:     []any{},
		ReferenceType:        referenceType,
		RequireSelection:     false,
		Required:             false,
	}
}

func buildShipmentCommodities(items []QuickQuoteLineItem) []shipmentCommodity {
	if len(items) == 0 {
		return []shipmentCommodity{}
	}

	out := make([]shipmentCommodity, 0, len(items))
	for _, item := range items {
		out = append(out, shipmentCommodity{
			PackageType:                      item.PackageType,
			CubicFeet:                        item.CubicFeet,
			CubicMeters:                      item.CubicMeters,
			PoundsPerCubicFeet:               item.PoundsPerCubicFeet,
			DimensionalWeight:                item.DimensionalWeight,
			ChargeableWeight:                 item.ChargeableWeight,
			EstimatedFreightClass:            item.EstimatedFreightClass,
			ShowEstimatedFreightClassMessage: item.ShowEstimatedFreightClassMessage,
			CubicFeetChanged:                 item.CubicFeetChanged,
			QuantityOfGoods:                  item.QuantityOfGoods,
			TotalWeight:                      item.TotalWeight,
			CommodityDescription:             item.CommodityDescription,
			SizeLength:                       item.SizeLength,
			SizeWidth:                        item.SizeWidth,
			SizeHeight:                       item.SizeHeight,
			FreightClass:                     item.FreightClass,
			NMFCNumber:                       item.NMFCNumber,
		})
	}

	return out
}

func summarizeShipmentCommodities(items []shipmentCommodity) shipmentTotals {
	var totals shipmentTotals

	for _, item := range items {
		totals.TotalWeight += item.TotalWeight
		totals.TotalHandlingUnits += item.QuantityOfGoods
		totals.TotalPieces += item.QuantityOfGoods
		if cf, err := strconv.ParseFloat(item.CubicFeet, 64); err == nil {
			totals.TotalCubicFeet += cf
		}
	}

	if totals.TotalCubicFeet > 0 {
		totals.TotalPCF = roundToTwoDecimals(float64(totals.TotalWeight) / totals.TotalCubicFeet)
	}

	if totals.TotalCubicFeet > 0 {
		totals.TotalCubicFeet = roundToTwoDecimals(totals.TotalCubicFeet)
	}

	return totals
}

func buildStopTimeFields(pickup, dropoff models.Stop) (stopTimeFields, stopTimeFields) {
	var pickupFields, dropoffFields stopTimeFields

	pickupFields.Ready, _ = formatStopTime(pickup.ReadyTime)
	pickupFields.Close, _ = formatStopTime(pickup.ReadyEndTime)
	if s, ok := formatStopTime(pickup.ApptStartTime); ok {
		pickupFields.ApptBegin = &s
	}
	if s, ok := formatStopTime(pickup.ApptEndTime); ok {
		pickupFields.ApptEnd = &s
	}

	dropoffFields.Ready, _ = formatStopTime(dropoff.ReadyTime)
	dropoffFields.Close, _ = formatStopTime(dropoff.MustDeliver)
	if s, ok := formatStopTime(dropoff.ApptStartTime); ok {
		dropoffFields.ApptBegin = &s
	}
	if s, ok := formatStopTime(dropoff.ApptEndTime); ok {
		dropoffFields.ApptEnd = &s
	}

	return pickupFields, dropoffFields
}

func formatStopTime(t models.NullTime) (string, bool) {
	if !t.Valid {
		return "", false
	}

	s := t.Time.Format(time.RFC3339)
	if s == defaultDateTimeSentinelUTC {
		return "", false
	}

	return s, true
}

func parseTaiDateTime(value string) (time.Time, bool) {
	if strings.TrimSpace(value) == "" {
		return time.Time{}, false
	}

	for _, layout := range []string{time.RFC3339, taiLegacyDateTimeLayout} {
		if parsed, err := time.Parse(layout, value); err == nil {
			return parsed, true
		}
	}

	return time.Time{}, false
}

func weightUnitDisplayName(weightUnit int) string {
	switch weightUnit {
	case 0:
		return "lbs"
	case 1:
		return "kg"
	default:
		return ""
	}
}

func dimensionUnitDisplayName(dimensionUnit int) string {
	switch dimensionUnit {
	case 0:
		return "in"
	case 1:
		return "cm"
	default:
		return ""
	}
}

func shipmentTypeDisplayName(shipmentType int) string {
	switch shipmentType {
	case 2:
		return "LTL"
	default:
		return ""
	}
}

func trailerTypeDisplayName(trailerType int) string {
	switch trailerType {
	case 40:
		return "Van"
	default:
		return ""
	}
}

func roundToTwoDecimals(value float64) float64 {
	return math.Round(value*100) / 100
}
