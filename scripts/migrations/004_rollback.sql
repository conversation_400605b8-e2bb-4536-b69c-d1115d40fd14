-- Rollback Script
-- Restores the database to its pre-migration state using backup tables
-- Only run this if the migration fails or needs to be reverted

BEGIN;

-- Restore quick_quote_agent_tasks from backup
TRUNCATE quick_quote_agent_tasks CASCADE;
INSERT INTO quick_quote_agent_tasks 
SELECT * FROM quick_quote_agent_tasks_backup;

-- Restore quick_quote_agent_task_invocations from backup
TRUNCATE quick_quote_agent_task_invocations CASCADE;
INSERT INTO quick_quote_agent_task_invocations 
SELECT * FROM quick_quote_agent_task_invocations_backup;

COMMIT;

-- Verification queries
SELECT 
    'Tasks restored' AS check_name,
    COUNT(*) AS count
FROM quick_quote_agent_tasks;

SELECT 
    'Invocations restored' AS check_name,
    COUNT(*) AS count
FROM quick_quote_agent_task_invocations;

SELECT 
    'pricingLookup tasks restored' AS check_name,
    COUNT(*) AS count
FROM quick_quote_agent_tasks 
WHERE action = 'pricingLookup';
