package openai

import (
	"context"
	"errors"

	openaiSDK "github.com/openai/openai-go/v2"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai/env"
)

type ErrEmbeddingDisabled struct {
	Message string
}

func (e *ErrEmbeddingDisabled) Error() string {
	return e.Message
}

// GetEmbedding creates an embedding vector (list of floating point numbers) for a given input string.
func (s *service) GetEmbedding(ctx context.Context, input string) (res []float64, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "openai.GetEmbedding", nil)
	defer func() { metaSpan.End(err) }()

	if !env.Vars.EnableVectorEmbeddings {
		return nil, &ErrEmbeddingDisabled{
			Message: "embedding disabled via ENABLE_VECTOR_EMBEDDINGS environment variable",
		}
	}

	if input == "" {
		return nil, errors.New("input must not be empty for embedding")
	}

	// OpenAI has a limit of 8192 tokens for embeddings
	truncatedInput := helpers.TokenTruncater(input, 8000)

	embedding, err := s.client.Embeddings.New(ctx, openaiSDK.EmbeddingNewParams{
		Model:          openaiSDK.EmbeddingModelTextEmbedding3Small,
		Input:          openaiSDK.EmbeddingNewParamsInputUnion{OfString: openaiSDK.String(truncatedInput)},
		EncodingFormat: openaiSDK.EmbeddingNewParamsEncodingFormatFloat,
	})
	if err != nil {
		return nil, err
	}

	if len(embedding.Data) < 1 {
		return nil, errors.New("no embedding data returned")
	}

	return embedding.Data[0].Embedding, nil
}
