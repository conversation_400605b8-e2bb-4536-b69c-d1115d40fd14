package tai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const customerAccessorialsEndpoint = "/api/shipmentDomestic/customerAccessorials"

type (
	accessorial struct {
		AccessorialCode            string  `json:"accessorialCode"`
		AccessorialDisplayType     int     `json:"accessorialDisplayType"`
		AccessorialID              int     `json:"accessorialId"`
		AccessorialName            string  `json:"accessorialName"`
		AccessorialType            int     `json:"accessorialType"`
		AccessorialTypeDisplayName string  `json:"accessorialTypeDisplayName"`
		Added                      bool    `json:"added,omitempty"`
		Buy                        float64 `json:"buy"`
		BuyModified                bool    `json:"buyModified"`
		ChargeLineType             int     `json:"chargeLineType"`
		ChargeLineTypeDisplayName  string  `json:"chargeLineTypeDisplayName"`
		PuertoRico                 bool    `json:"puertoRico"`
		Selected                   bool    `json:"selected"`
		SelectedNotVisible         bool    `json:"selectedNotVisible"`
		Sell                       float64 `json:"sell"`
		SellModified               bool    `json:"sellModified"`
	}

	// staticMeta provides curated actions/category overlays that we cannot infer from their API.
	staticMeta struct {
		ExternalID   int
		Name         string
		CategoryTags []models.CategoryTag
		Actions      map[string]models.FormType
	}

	customerAccessorialsRequestBody struct {
		ShipmentID             *int `json:"shipmentId"`
		CustomerOrganizationID int  `json:"customerOrganizationId"`
		BrokerID               int  `json:"brokerId"`
		ShipmentType           int  `json:"shipmentType"`
	}
)

// Static overlay keyed by accessorial code with curated actions/categories.
var accessorialStaticByCode = map[string]staticMeta{
	// ---------------------------
	// Pickup-related accessorials
	// ---------------------------

	// Airport PU
	"100P": {
		ExternalID:   68,
		Name:         "Airport PU",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
		Actions: map[string]models.FormType{
			"MAWB Number":         models.FormTypeText,
			"Load Release Number": models.FormTypeText,
			"Last Free Day":       models.FormTypeDate,
		},
	},

	// Appointment (Pickup)
	"APTP": {
		ExternalID:   41,
		Name:         "Appointment",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// CFS Pickup
	"CFSP": {
		ExternalID:   42,
		Name:         "CFS Pickup",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
		Actions: map[string]models.FormType{
			"MAWB Number":         models.FormTypeText,
			"Load Release Number": models.FormTypeText,
			"Last Free Day":       models.FormTypeDate,
		},
	},

	// Construction Site (Pickup)
	"CONTRUCP": {
		ExternalID:   43,
		Name:         "Construction Site",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Grocery Warehouse (Pickup)
	"GWP": {
		ExternalID:   502,
		Name:         "Grocery Warehouse",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Hazardous Material (Pickup)
	"HAZP": {
		ExternalID:   46,
		Name:         "Hazardous Material",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Hospital Pickup
	"HOSPPICK": {
		ExternalID:   437,
		Name:         "Hospital Pickup",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Inside Pickup
	"IPU": {
		ExternalID:   48,
		Name:         "Inside Pickup",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Liftgate Pickup
	"LFTP": {
		ExternalID:   50,
		Name:         "Liftgate",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Limited Access Pickup
	"ACHP": {
		ExternalID:   51,
		Name:         "Limited Access Pickup",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Military Base (Pickup)
	"MILPU": {
		ExternalID:   88,
		Name:         "Military Base",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Mine Site Pickup
	"MSP": {
		ExternalID:   478,
		Name:         "Mine Site Pickup",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Notification (Pickup)
	"APTN": {
		ExternalID:   53,
		Name:         "Notification",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Protect from Freezing (Pickup)
	"PSCP": {
		ExternalID:   54,
		Name:         "Protect from Freezing",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Residential (Pickup)
	"REP": {
		ExternalID:   55,
		Name:         "Residential",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
	},

	// Trade Show (Pickup)
	"EBP": {
		ExternalID:   57,
		Name:         "Trade Show",
		CategoryTags: []models.CategoryTag{models.CategoryPickup},
		Actions: map[string]models.FormType{
			"Show Name":      models.FormTypeText,
			"Exhibitor Name": models.FormTypeText,
			"Booth Number":   models.FormTypeText,
			"Decorator":      models.FormTypeText,
		},
	},

	// -----------------------------
	// Delivery-related accessorials
	// -----------------------------

	// Airport Delivery
	"100D": {
		ExternalID:   189,
		Name:         "Airport DEL",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
		Actions:      map[string]models.FormType{"Booking Number": models.FormTypeText},
	},

	// Appointment (Delivery)
	"APTD": {
		ExternalID:   18,
		Name:         "Appointment",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// CFS Delivery
	"CFSD": {
		ExternalID:   19,
		Name:         "CFS",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
		Actions:      map[string]models.FormType{"Booking Number": models.FormTypeText},
	},

	// Church Delivery
	"CHUD": {
		ExternalID:   332,
		Name:         "Church Delivery",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Construction Site (Delivery)
	"CONTRUCD": {
		ExternalID:   20,
		Name:         "Construction Site",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Expedited Delivery
	"EXPEDDEL": {
		ExternalID:   21,
		Name:         "Expedited Delivery",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Grocery Warehouse (Delivery)
	"GWHSED": {
		ExternalID:   479,
		Name:         "Grocery Warehouse",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Inside Delivery
	"IDL": {
		ExternalID:   26,
		Name:         "Inside Delivery",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Liftgate Delivery
	"LFTD": {
		ExternalID:   28,
		Name:         "Liftgate",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Limited Access Delivery
	"ACHD": {
		ExternalID:   29,
		Name:         "Limited Access Delivery",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Lumper Fee
	"LUMPERDE": {
		ExternalID:   16,
		Name:         "Lumper Fee",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Military Base (Delivery)
	"MILDEL": {
		ExternalID:   89,
		Name:         "Military Base",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Mine Site Delivery
	"MSD": {
		ExternalID:   112,
		Name:         "Mine Site Delivery",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Notification (Delivery)
	"MNC": {
		ExternalID:   30,
		Name:         "Notification",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Over Length (Delivery)
	"OVR8TO12": {
		ExternalID:   326,
		Name:         "Over Length - 8ft but less than 12ft",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},
	"OVR12TO16": {
		ExternalID:   327,
		Name:         "Over Length - 12ft but less than 16ft",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},
	"OVR16TO20": {
		ExternalID:   328,
		Name:         "Over Length - 16ft but less than 20ft",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},
	"OVR20": {
		ExternalID:   329,
		Name:         "Over Length - 20ft to max",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Protect from Freezing (Delivery)
	"PSCD": {
		ExternalID:   293,
		Name:         "Protect from Freezing",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Residential (Delivery)
	"RESD": {
		ExternalID:   31,
		Name:         "Residential",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Residential - Appointment Forward Air (Delivery)
	"RESDFA": {
		ExternalID:   366,
		Name:         "Residential - Appointment Forward Air",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Sort and Seg (Delivery)
	"SEGD": {
		ExternalID:   368,
		Name:         "Sort and Seg",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
	},

	// Trade Show (Delivery)
	"EBD": {
		ExternalID:   33,
		Name:         "Trade Show",
		CategoryTags: []models.CategoryTag{models.CategoryDelivery},
		Actions: map[string]models.FormType{
			"Show Name":      models.FormTypeText,
			"Exhibitor Name": models.FormTypeText,
			"Booth Number":   models.FormTypeText,
			"Decorator":      models.FormTypeText,
		},
	},

	// -----------------------------
	// Shipment-related accessorials
	// -----------------------------

	// Chassis Fee
	"FCS": {
		ExternalID:   127,
		Name:         "Chassis Fee",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Cubic Capacity
	"CC": {
		ExternalID:   294,
		Name:         "Cubic Capacity",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Detention
	"DTUD": {
		ExternalID:   157,
		Name:         "Detention",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Dry Run
	"DRYRUN": {
		ExternalID:   101,
		Name:         "Dry Run",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Guaranteed Delivery
	"680": {
		ExternalID:   193,
		Name:         "Guaranteed Delivery",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Inspection Fee
	"IAC": {
		ExternalID:   79,
		Name:         "Inspection Fee",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Minimum Charge
	"MIC": {
		ExternalID:   449,
		Name:         "Minimum Charge",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Re-Class
	"RECLASS": {
		ExternalID:   249,
		Name:         "Re-Class",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Reconsignment
	"RCC": {
		ExternalID:   5,
		Name:         "Reconsignment",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Redelivery Charge
	"RCL": {
		ExternalID:   10,
		Name:         "Redelivery Charge",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Reweigh
	"EXW": {
		ExternalID:   11,
		Name:         "Reweigh",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Sort and Segregation
	"SEG": {
		ExternalID:   32,
		Name:         "Sort and Segregation",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Storage
	"SRG": {
		ExternalID:   225,
		Name:         "Storage",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Storage Fee
	"SRGF": {
		ExternalID:   98,
		Name:         "Storage Fee",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// TONU (Truck Ordered Not Used)
	"TONU": {
		ExternalID:   104,
		Name:         "TONU",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},

	// Warehouse Fees
	"WHC": {
		ExternalID:   66,
		Name:         "Warehouse Fees",
		CategoryTags: []models.CategoryTag{models.CategoryShipment},
	},
}

// GetCustomerAccessorials fetches dynamic accessorials from Tai and overlays curated metadata.
// It returns an enriched list of accessorials containing id, name, code, categories, and actions.
func (c *Client) GetCustomerAccessorials(
	ctx context.Context,
	customerOrgID,
	brokerID,
	shipmentType int,
) ([]models.Accessorial, error) {

	reqBody := customerAccessorialsRequestBody{
		ShipmentID:             nil,
		CustomerOrganizationID: customerOrgID,
		BrokerID:               brokerID,
		ShipmentType:           shipmentType,
	}

	payload, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal accessorial request body: %w", err)
	}
	body := bytes.NewBuffer(payload)

	var accessorials []accessorial
	if _, err := c.post(ctx, customerAccessorialsEndpoint, body, &accessorials); err != nil {
		log.Warn(ctx, "Tai customerAccessorials API failed, reverting to static overlay", zap.Error(err))
		return c.GetStaticCustomerAccessorials(), nil
	}

	if len(accessorials) == 0 {
		log.Warn(ctx, "Tai customerAccessorials API returned zero results, reverting to static overlay")
		return c.GetStaticCustomerAccessorials(), nil
	}

	out := make([]models.Accessorial, 0, len(accessorials))
	for _, a := range accessorials {
		code := strings.ToUpper(strings.TrimSpace(a.AccessorialCode))
		meta, hasMeta := accessorialStaticByCode[code]

		acc := models.Accessorial{
			ExternalID:   a.AccessorialID,
			Name:         pickValue(meta.Name, a.AccessorialName),
			Code:         a.AccessorialCode,
			CategoryTags: nil,
			Actions:      nil,
		}

		if hasMeta && len(meta.CategoryTags) > 0 {
			acc.CategoryTags = meta.CategoryTags
		} else {
			acc.CategoryTags = inferCategoriesFromType(a.AccessorialTypeDisplayName)
		}

		if hasMeta && meta.Actions != nil {
			acc.Actions = meta.Actions
		}

		out = append(out, acc)
	}

	return sortedAccessorials(out), nil
}

// GetStaticCustomerAccessorials provides a fallback list using curated static data.
func (c *Client) GetStaticCustomerAccessorials() []models.Accessorial {
	out := make([]models.Accessorial, 0, len(accessorialStaticByCode))

	for code, meta := range accessorialStaticByCode {
		out = append(out, models.Accessorial{
			ExternalID:   meta.ExternalID,
			Code:         code,
			Name:         meta.Name,
			CategoryTags: meta.CategoryTags,
			Actions:      meta.Actions,
		})
	}

	return sortedAccessorials(out)
}

// sortedAccessorials returns a new slice sorted by category (pickup -> delivery -> shipment), then alphabetically
// by name.
func sortedAccessorials(accessorials []models.Accessorial) []models.Accessorial {
	categoryOrder := map[models.CategoryTag]int{
		models.CategoryPickup:   0,
		models.CategoryDelivery: 1,
		models.CategoryShipment: 2,
	}

	result := make([]models.Accessorial, len(accessorials))
	copy(result, accessorials)

	sort.Slice(result, func(i, j int) bool {
		iOrder, jOrder := 99, 99

		if len(result[i].CategoryTags) > 0 {
			if o, ok := categoryOrder[result[i].CategoryTags[0]]; ok {
				iOrder = o
			}
		}

		if len(result[j].CategoryTags) > 0 {
			if o, ok := categoryOrder[result[j].CategoryTags[0]]; ok {
				jOrder = o
			}
		}

		if iOrder != jOrder {
			return iOrder < jOrder
		}

		return strings.ToLower(result[i].Name) < strings.ToLower(result[j].Name)
	})

	return result
}

// inferCategoriesFromType maps Tai's AccessorialTypeDisplayName to our internal categories.
func inferCategoriesFromType(t string) []models.CategoryTag {
	switch strings.ToLower(strings.TrimSpace(t)) {
	case "pickup":
		return []models.CategoryTag{models.CategoryPickup}
	case "delivery":
		return []models.CategoryTag{models.CategoryDelivery}
	default:
		return []models.CategoryTag{models.CategoryShipment}
	}
}

func pickValue(primary, fallback string) string {
	if strings.TrimSpace(primary) != "" {
		return primary
	}

	return fallback
}
