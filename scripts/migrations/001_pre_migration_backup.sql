-- Pre-Migration Backup Script
-- Captures current state for rollback if needed
-- Run this BEFORE running the main migration

-- Capture current state for rollback
CREATE TABLE IF NOT EXISTS quick_quote_agent_tasks_backup AS 
SELECT * FROM quick_quote_agent_tasks;

CREATE TABLE IF NOT EXISTS quick_quote_agent_task_invocations_backup AS 
SELECT * FROM quick_quote_agent_task_invocations;

-- Verify backups were created
SELECT 
    'quick_quote_agent_tasks_backup' AS table_name,
    COUNT(*) AS row_count
FROM quick_quote_agent_tasks_backup
UNION ALL
SELECT 
    'quick_quote_agent_task_invocations_backup' AS table_name,
    COUNT(*) AS row_count
FROM quick_quote_agent_task_invocations_backup;
