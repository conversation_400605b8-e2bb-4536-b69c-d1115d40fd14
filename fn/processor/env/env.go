package env

import (
	"context"
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/awsutil"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
)

var Vars envVars

type envVars struct {
	rds.EnvConfig

	// Deployment stage: "dev" or "prod"
	AppEnv string `envconfig:"APP_ENV" required:"true"`

	RedisURL string `envconfig:"REDIS_URL"`
	TraceOn  bool   `envconfig:"TRACE_ON"`
	// If true, messages with the same RFC ID are processed just once, then data is copied to DB for copied users
	// If false, messages with the same RFC ID are processed E2E
	CopyDuplicateEmails bool `envconfig:"COPY_DUPLICATE_EMAILS" default:"true"`

	// Set to false to regenerate suggestions for already processed emails, such as if you're backfilling after a bug
	SkipDuplicateSuggestions bool `envconfig:"SKIP_DUPLICATE_SUGGESTIONS" default:"true"`

	// Application Observability
	AxiomLogDataset   string `envconfig:"AXIOM_LOG_DATASET"`
	AxiomTraceDataset string `envconfig:"AXIOM_TRACE_DATASET"`
	AxiomOrgID        string `envconfig:"AXIOM_ORG_ID"`
	AxiomToken        string `envconfig:"AXIOM_TOKEN"`

	// LLM Observability (optional in dev, required in prod)
	BraintrustBaseURL string `envconfig:"BRAINTRUST_BASE_URL"`
	BraintrustAPIKey  string `envconfig:"BRAINTRUST_API_KEY"`

	GoogleClientID string `envconfig:"GOOGLE_CLIENT_ID"`

	// AWS Region for LLM on SageMaker
	LLMEndpoint string `envconfig:"LLM_ENDPOINT"`

	AljexLambda string `envconfig:"ALJEX_LAMBDA"`

	// Cyclops EC2 private address
	CyclopsURL string `envconfig:"CYCLOPS_URL"`

	// SendEmail SQS queue URL
	SendEmailSQSURL string `envconfig:"SEND_EMAIL_SQS_URL"`

	// For prod only
	SecretARN string `envconfig:"SECRET_ARN"`

	// In dev, these are set directly.
	// In prod, this is loaded from SecretARN at startup.
	GoogleClientSecret string `envconfig:"GOOGLE_CLIENT_SECRET"`

	ProfilingEnabled bool `envconfig:"PROFILING_ENABLED" default:"false"`

	// Maximum number of load building extractors to run in parallel
	MaxConcurrentLoadBuildingExtractors int `envconfig:"MAX_CONCURRENT_LOAD_BUILDING_EXTRACTORS" default:"6"`
}

// Format in AWS secrets manager
type envSecrets struct {
	AESKey             string `json:"AES_KEY"`
	OpenAIAPIKey       string `json:"OPENAI_API_KEY"`
	GoogleClientSecret string `json:"GOOGLE_CLIENT_SECRET"`
}

// Load environment variables into Vars global
func Load(ctx context.Context) error {
	if stage := os.Getenv("APP_ENV"); stage == "" || stage == "dev" {
		if err := godotenv.Load(); err != nil {
			log.Warn(ctx, "no .env file found", zap.Error(err))
		}
		ctx = log.NewFromEnv(ctx)
	}

	if err := envconfig.Process("", &Vars); err != nil {
		return err
	}

	switch Vars.AppEnv {
	case "dev":
		if Vars.RedisURL == "" {
			log.WarnNoSentry(ctx, "missing Redis URL")
		}

	case "prod", "staging":
		var secret envSecrets

		if err := awsutil.ReadSecretJSON(ctx, Vars.SecretARN, &secret); err != nil {
			return err
		}

		if secret.AESKey == "" || secret.GoogleClientSecret == "" {
			return fmt.Errorf("%s is missing some fields", Vars.SecretARN)
		}

		crypto.AESKey = []byte(secret.AESKey)
		Vars.GoogleClientSecret = secret.GoogleClientSecret
	}

	return nil
}
