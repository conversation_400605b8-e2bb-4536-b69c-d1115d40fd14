package tmsbrokerstaff

import (
	"context"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

type SearchBrokerStaffQuery struct {
	TMSID          uint
	OrganizationID int
	Term           string
	Limit          int
}

func SearchBrokerStaff(ctx context.Context, query SearchBrokerStaffQuery) ([]models.TMSBrokerStaff, error) {
	res := []models.TMSBrokerStaff{}
	db := rds.WithContextReader(ctx).Where("tms_integration_id = ?", query.TMSID)

	if query.OrganizationID != 0 {
		db = db.Where("organization_id = ?", query.OrganizationID)
	}

	term := strings.TrimSpace(query.Term)
	if term != "" {
		like := "%%" + term + "%%"
		db = db.Where(
			"contact_name ILIKE ? OR email ILIKE ? OR login ILIKE ?",
			like,
			like,
			like,
		)
	}

	limit := query.Limit
	if limit <= 0 {
		limit = 20
	}

	db = db.Order("contact_name ASC").Limit(limit)

	return res, db.Find(&res).Error
}
