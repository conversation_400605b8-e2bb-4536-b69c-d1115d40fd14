package quoteprivate

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/customer/trident"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	quote "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
)

type (
	LaneRateBody struct {
		EmailID        uint                 `json:"emailId"`
		ThreadID       string               `json:"threadId"`
		QuoteRequestID uint                 `json:"quoteRequestId"`
		TransportType  models.TransportType `json:"transportType"`
		OriginDate     time.Time            `json:"originDate"` // Keep
		// TODO: After FE users are past v0.48.2 we can remove the legacy origin/destination location fields
		OriginZip        string    `json:"originZip"`
		OriginCity       string    `json:"originCity"`
		OriginState      string    `json:"originState"`
		DestinationDate  time.Time `json:"destinationDate"` // Keep
		DestinationZip   string    `json:"destinationZip"`
		DestinationCity  string    `json:"destinationCity"`
		DestinationState string    `json:"destinationState"`
		// Multi-stop support (matches QuickQuoteV2 request convention)
		// If provided, these take precedence over legacy origin/destination fields
		Stops []Stop `json:"stops"`
	}

	LaneRateResponse struct {
		RateType string `json:"rateType"`
		// Per Trip data
		RatePerTrip float64 `json:"ratePerTrip"`
		HighPerTrip float64 `json:"highPerTrip"`
		LowPerTrip  float64 `json:"lowPerTrip"`
		// Per Mile data
		RatePerMile float64 `json:"ratePerMile"`
		HighPerMile float64 `json:"highPerMile"`
		LowPerMile  float64 `json:"lowPerMile"`
		// Timeframe and geographical data
		Timeframe       string `json:"timeframe"`
		Companies       int64  `json:"companies"`
		Reports         int64  `json:"reports"`
		OriginName      string `json:"originName"`
		OriginType      string `json:"originType"`
		DestinationName string `json:"destinationName"`
		DestinationType string `json:"destinationType"`
		// Quotes contains one or more quote variants (e.g., leg-to-leg, longest-leg)
		// for multi-stop requests. For two-stop requests, it will contain a single
		// DAT spot quote.
		Quotes []Quote `json:"quotes,omitempty"`
	}
)

const (
	TridentTransportID = 562
)

func GetLaneRateFromService(c *fiber.Ctx) error {
	var body LaneRateBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext())
	claims := middleware.ClaimsFromContext(c)

	service, err := rds.GetServiceWithPreload(ctx, *claims.ServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !service.IsGetLaneRateFromServiceEnabled {
		log.Error(ctx, "getting lane rate from service is not enabled")
		return c.SendStatus(http.StatusForbidden)
	}

	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		log.Error(ctx, "could not get user", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// No feature flag here because multiple services may use this feature in the future so we need
	// a different way to enable it for each service.
	if service.ID != TridentTransportID {
		log.Warn(ctx, "skipping lane rate from service since service is not Trident")
		return c.Status(http.StatusBadRequest).SendString("Unrecognized Service")
	}

	stops, buildStopsErr := buildStopsFromBody(ctx, service, body)
	if buildStopsErr != nil {
		log.Error(ctx, "failed to build stops", zap.Error(buildStopsErr))
		return c.Status(http.StatusBadRequest).SendString(buildStopsErr.Error())
	}

	if len(stops) < 2 {
		log.Warn(ctx, "insufficient stops provided", zap.Int("numStops", len(stops)))
		return c.Status(http.StatusBadRequest).SendString("at least two stops are required")
	}

	updatedBody := fillEmptyDatesWithDefaults(body)
	body = updatedBody

	// Execute lane rate retrieval via Trident for 2-stop or multi-stop
	laneResp, aggNumeric, legsCompanies, legsReports, legs, err := getTridentLaneRateForStops(
		ctx,
		service,
		stops,
		body.TransportType,
	)
	if err != nil {
		log.Error(ctx, "unable to get Trident lane rate", zap.Error(err))
		return c.Status(http.StatusInternalServerError).
			SendString("Trident's DAT integration wasn't able to get a lane rate")
	}

	rateDate := quote.RateData{
		ExternalID:      "",
		TargetBuyRate:   aggNumeric.RatePerTrip,
		LowBuyRate:      aggNumeric.LowPerTrip,
		HighBuyRate:     aggNumeric.HighPerTrip,
		StartBuyRate:    0,
		FuelRate:        aggNumeric.AvgFuelPerTrip,
		Distance:        aggNumeric.Mileage,
		ConfidenceLevel: aggNumeric.Confidence,
	}

	quoteRecord := createServiceQuoteRecord(
		ctx,
		service,
		user.ID,
		body.EmailID,
		body.ThreadID,
		body.QuoteRequestID,
		stops,
		body.TransportType,
		body.OriginDate,
		body.DestinationDate,
		rateDate,
		aggNumeric,
		laneResp,
	)
	if quoteRecord == nil {
		log.Error(ctx, "unable to create quote record for Trident DAT")
		return c.Status(http.StatusInternalServerError).
			SendString("error while creating quote for Trident's DAT integration")
	}

	companies, err := laneResp.Companies.Int64()
	if err != nil {
		log.Warn(ctx, "error converting Olympus company count to int64", zap.Error(err))
		companies = legsCompanies
	}

	reports, err := laneResp.Reports.Int64()
	if err != nil {
		log.Warn(ctx, "error converting Olympus report count to int64", zap.Error(err))
		reports = legsReports
	}

	var quotes []Quote

	if len(stops) == 2 {
		quotes = append(quotes, buildSpotQuote(
			aggNumeric,
			laneResp.Timeframe,
			laneResp.OriginName,
			laneResp.OriginType,
			laneResp.DestinationName,
			laneResp.DestinationType,
			reports,
			companies,
		))
	} else if len(stops) > 2 {
		quotes = append(quotes, buildLegToLegQuote(aggNumeric, laneResp, reports, companies, legs))

		q, err := buildLongestLegQuote(ctx, user, service, stops, body.TransportType, aggNumeric)
		if err != nil {
			log.Warn(ctx, "longest-leg quote build failed", zap.Error(err))
		} else if q != nil {
			quotes = append(quotes, *q)
		}
	}

	return c.Status(http.StatusOK).JSON(LaneRateResponse{
		RateType:  laneResp.RateType,
		Companies: companies,
		Reports:   reports,
		// Per Trip data
		RatePerTrip: aggNumeric.RatePerTrip,
		LowPerTrip:  aggNumeric.LowPerTrip,
		HighPerTrip: aggNumeric.HighPerTrip,
		// Per Mile data
		RatePerMile: aggNumeric.RatePerMile,
		LowPerMile:  aggNumeric.LowPerMile,
		HighPerMile: aggNumeric.HighPerMile,
		// Timeframe and geographical data (best-effort for multi-stop)
		Timeframe:       laneResp.Timeframe,
		OriginName:      laneResp.OriginName,
		OriginType:      laneResp.OriginType,
		DestinationName: laneResp.DestinationName,
		DestinationType: laneResp.DestinationType,
		Quotes:          quotes,
	})
}

// GetServiceQuotes returns Quote objects using the service's customer integration (e.g. Trident)
// It supports both 2-stop and multi-stop requests and creates quote records.
func GetServiceQuotes(
	ctx context.Context,
	service models.Service,
	user models.User,
	emailID uint,
	threadID string,
	quoteRequestID uint,
	stops []models.Stop,
	transportType models.TransportType,
	pickupDate,
	deliveryDate time.Time,
) ([]Quote, error) {

	laneResp, aggNumeric, _, _, _, err := getTridentLaneRateForStops(ctx, service, stops, transportType)
	if err != nil {
		return nil, err
	}

	rateData := quote.RateData{
		ExternalID:      "",
		TargetBuyRate:   aggNumeric.RatePerTrip,
		LowBuyRate:      aggNumeric.LowPerTrip,
		HighBuyRate:     aggNumeric.HighPerTrip,
		FuelRate:        aggNumeric.AvgFuelPerTrip,
		Distance:        aggNumeric.Mileage,
		ConfidenceLevel: aggNumeric.Confidence,
	}

	created := quote.CreateQuoteRecord(
		ctx,
		service,
		user.ID,
		emailID,
		threadID,
		quoteRequestID,
		stops,
		transportType,
		pickupDate,
		deliveryDate,
		rateData,
		aggNumeric.RatePerTrip,
		aggNumeric.LowPerTrip,
		aggNumeric.HighPerTrip,
		"",
		"",
		models.DATSource,
		helpers.Ternary(len(stops) > 2, models.DATMultiStopLegToLeg, models.DATSpotType),
		&models.QuoteDATMetadata{
			DATTimeframe:       laneResp.Timeframe,
			DATOriginName:      laneResp.OriginName,
			DATOriginType:      laneResp.OriginType,
			DATDestinationName: laneResp.DestinationName,
			DATDestinationType: laneResp.DestinationType,
		},
	)

	var createdID uint
	if created != nil {
		createdID = created.ID
	}

	quoteOut := Quote{
		ID:     createdID,
		Source: models.DATSource,
		Type:   helpers.Ternary(len(stops) > 2, models.DATMultiStopLegToLeg, models.DATSpotType),
		Rates: RateValues{
			Target:        aggNumeric.RatePerTrip,
			Low:           aggNumeric.LowPerTrip,
			High:          aggNumeric.HighPerTrip,
			TargetPerMile: aggNumeric.RatePerMile,
			LowPerMile:    aggNumeric.LowPerMile,
			HighPerMile:   aggNumeric.HighPerMile,
		},
		Distance: aggNumeric.Mileage,
		Metadata: map[string]any{
			"timeframe":            laneResp.Timeframe,
			"originName":           laneResp.OriginName,
			"originType":           laneResp.OriginType,
			"destinationName":      laneResp.DestinationName,
			"destinationType":      laneResp.DestinationType,
			"fuelSurchargePerTrip": aggNumeric.AvgFuelPerTrip,
			"fuelSurchargePerMile": helpers.Ternary(
				aggNumeric.Mileage != 0,
				aggNumeric.AvgFuelPerTrip/aggNumeric.Mileage,
				0.0,
			),
			"viaService": "Trident",
		},
	}

	return []Quote{quoteOut}, nil
}

// getTridentLaneRateForStops calls the Trident-backed lane rate for either 2-stop or multi-stop,
// returning an aggregate numerical summary. For multi-stop, legs are summed.
func getTridentLaneRateForStops(
	ctx context.Context,
	service models.Service,
	stops []models.Stop,
	transportType models.TransportType,
) (*trident.LaneRateResponse, *trident.LaneRateNumerical, int64, int64, []RouteLeg, error) {

	integration, err := integrationDB.GetByServiceIDAndType(ctx, service.ID, models.CustomerType)
	if err != nil {
		return nil, nil, 0, 0, nil, fmt.Errorf("integration lookup failed: %w", err)
	}

	client, err := trident.New(ctx, integration)
	if err != nil {
		return nil, nil, 0, 0, nil, fmt.Errorf("failed to create trident client: %w", err)
	}

	if len(stops) < 2 {
		return nil, nil, 0, 0, nil, errors.New("at least two stops are required")
	}

	// Two-stop fast path
	if len(stops) == 2 {
		resp, err := client.GetLaneRate(
			ctx,
			trident.LaneRateRequest{
				OriginZip:      stops[0].Address.Zip,
				DestinationZip: stops[1].Address.Zip,
				Equipment:      string(transportType),
			},
		)
		if err != nil {
			return nil, nil, 0, 0, nil, err
		}

		numeric, err := trident.ParseCalculateLaneRate(ctx, resp)
		if err != nil {
			return nil, nil, 0, 0, nil, err
		}

		companies, err := resp.Companies.Int64()
		if err != nil {
			log.Warn(ctx, "error converting Olympus company count to int64", zap.Error(err))
			companies = 0
		}

		reports, err := resp.Reports.Int64()
		if err != nil {
			log.Warn(ctx, "error converting Olympus report count to int64", zap.Error(err))
			reports = 0
		}

		// Single leg details
		var legs []RouteLeg
		perMileTarget := 0.0
		perMileLow := 0.0
		perMileHigh := 0.0

		if numeric.Mileage > 0 {
			perMileTarget = numeric.RatePerTrip / numeric.Mileage
			perMileLow = numeric.LowPerTrip / numeric.Mileage
			perMileHigh = numeric.HighPerTrip / numeric.Mileage
		}

		legs = append(legs, RouteLeg{
			Order:          0,
			StartStopIndex: 0,
			EndStopIndex:   1,
			StartCityState: stops[0].Address.City + ", " + stops[0].Address.State,
			EndCityState:   stops[1].Address.City + ", " + stops[1].Address.State,
			DistanceMiles:  numeric.Mileage,
			Rates: RateValues{
				Target:        numeric.RatePerTrip,
				Low:           numeric.LowPerTrip,
				High:          numeric.HighPerTrip,
				TargetPerMile: perMileTarget,
				LowPerMile:    perMileLow,
				HighPerMile:   perMileHigh,
			},
		})

		return resp, &numeric, companies, reports, legs, nil
	}

	// Multi-stop: sum legs
	var agg trident.LaneRateNumerical
	var firstResp *trident.LaneRateResponse
	var aggCompanies int64
	var aggReports int64
	var routeLegs []RouteLeg

	for i := 0; i < len(stops)-1; i++ {
		start := stops[i]
		end := stops[i+1]
		resp, err := client.GetLaneRate(ctx, trident.LaneRateRequest{
			OriginZip:      start.Address.Zip,
			DestinationZip: end.Address.Zip,
			Equipment:      string(transportType),
		})
		if err != nil {
			return nil, nil, 0, 0, nil, fmt.Errorf("trident leg %d request failed: %w", i, err)
		}

		numeric, err := trident.ParseCalculateLaneRate(ctx, resp)
		if err != nil {
			return nil, nil, 0, 0, nil, fmt.Errorf("trident leg %d parse failed: %w", i, err)
		}

		if firstResp == nil {
			firstResp = resp
			agg = numeric
		} else {
			agg.RatePerTrip += numeric.RatePerTrip
			agg.LowPerTrip += numeric.LowPerTrip
			agg.HighPerTrip += numeric.HighPerTrip
			agg.Mileage += numeric.Mileage
			agg.AvgFuelPerTrip += numeric.AvgFuelPerTrip
		}

		c, err := resp.Companies.Int64()
		if err != nil {
			log.Warn(ctx, "error converting Olympus company count to int64 (leg)", zap.Error(err))
			c = 0
		}

		r, err := resp.Reports.Int64()
		if err != nil {
			log.Warn(ctx, "error converting Olympus report count to int64 (leg)", zap.Error(err))
			r = 0
		}

		aggCompanies += c
		aggReports += r

		// Capture per-leg details
		perMileTarget := 0.0
		perMileLow := 0.0
		perMileHigh := 0.0

		if numeric.Mileage > 0 {
			perMileTarget = numeric.RatePerTrip / numeric.Mileage
			perMileLow = numeric.LowPerTrip / numeric.Mileage
			perMileHigh = numeric.HighPerTrip / numeric.Mileage
		}

		routeLegs = append(routeLegs, RouteLeg{
			Order:          i,
			StartStopIndex: i,
			EndStopIndex:   i + 1,
			StartCityState: start.Address.City + ", " + start.Address.State,
			EndCityState:   end.Address.City + ", " + end.Address.State,
			DistanceMiles:  numeric.Mileage,
			Rates: RateValues{
				Target:        numeric.RatePerTrip,
				Low:           numeric.LowPerTrip,
				High:          numeric.HighPerTrip,
				TargetPerMile: perMileTarget,
				LowPerMile:    perMileLow,
				HighPerMile:   perMileHigh,
			},
		})
	}

	// Calculate per-mile rates from aggregated totals
	if agg.Mileage > 0 {
		agg.RatePerMile = agg.RatePerTrip / agg.Mileage
		agg.LowPerMile = agg.LowPerTrip / agg.Mileage
		agg.HighPerMile = agg.HighPerTrip / agg.Mileage
	}

	return firstResp, &agg, aggCompanies, aggReports, routeLegs, nil
}

// buildStopsFromBody determines stops using the same conventions as QuickQuoteV2.
// It prefers multi-stop arrays if provided and enabled by the service; otherwise falls back
// to legacy origin/destination fields with ZIP lookups as needed.
func buildStopsFromBody(
	ctx context.Context,
	service models.Service,
	body LaneRateBody,
) ([]models.Stop, error) {

	if service.IsMultiStopQuickQuoteEnabled && len(body.Stops) > 0 {
		stops, _, err := convertAndValidateLegacyStops(ctx, body.Stops)
		if err != nil {
			return nil, err
		}
		return stops, nil
	}

	originZip := body.OriginZip
	destinationZip := body.DestinationZip

	if originZip == "" {
		zipCode, err := helpers.LookupZipCodeByCityState(ctx, body.OriginCity, body.OriginState)
		if err != nil {
			return nil, fmt.Errorf("lookup origin zip: %w", err)
		}

		if zipCode == "" {
			return nil, fmt.Errorf("no origin zip for %s, %s", body.OriginCity, body.OriginState)
		}

		originZip = zipCode
	}

	if destinationZip == "" {
		zipCode, err := helpers.LookupZipCodeByCityState(ctx, body.DestinationCity, body.DestinationState)
		if err != nil {
			return nil, fmt.Errorf("lookup destination zip: %w", err)
		}

		if zipCode == "" {
			return nil, fmt.Errorf("no destination zip for %s, %s", body.DestinationCity, body.DestinationState)
		}

		destinationZip = zipCode
	}

	stops := []models.Stop{
		{
			Order:      0,
			StopNumber: 0,
			Address: models.Address{
				City:  body.OriginCity,
				State: body.OriginState,
				Zip:   originZip,
			},
		},
		{
			Order:      1,
			StopNumber: 1,
			Address: models.Address{
				City:  body.DestinationCity,
				State: body.DestinationState,
				Zip:   destinationZip,
			},
		},
	}

	return stops, nil
}

// fillEmptyDatesWithDefaults fills in pickup and delivery dates when not provided in request from FE.
// Returns an updated body with default dates filled in.
func fillEmptyDatesWithDefaults(body LaneRateBody) LaneRateBody {
	updatedBody := body

	if updatedBody.OriginDate.IsZero() {
		if !updatedBody.DestinationDate.IsZero() {
			updatedBody.OriginDate = updatedBody.DestinationDate.AddDate(0, 0, -1)
		} else {
			updatedBody.OriginDate = time.Now().AddDate(0, 0, 1)
		}
	}

	if updatedBody.DestinationDate.IsZero() {
		if !updatedBody.OriginDate.IsZero() {
			updatedBody.DestinationDate = updatedBody.OriginDate.AddDate(0, 0, 1)
		} else {
			updatedBody.DestinationDate = time.Now().AddDate(0, 0, 2)
		}
	}

	return updatedBody
}

// createServiceQuoteRecord wraps quote.CreateQuoteRecord and returns the created quote pointer.
func createServiceQuoteRecord(
	ctx context.Context,
	service models.Service,
	userID uint,
	emailID uint,
	threadID string,
	quoteRequestID uint,
	stops []models.Stop,
	transportType models.TransportType,
	pickupDate time.Time,
	deliveryDate time.Time,
	rateData quote.RateData,
	avgNumeric *trident.LaneRateNumerical,
	laneResp *trident.LaneRateResponse,
) *models.QuickQuote {

	return quote.CreateQuoteRecord(
		ctx,
		service,
		userID,
		emailID,
		threadID,
		quoteRequestID,
		stops,
		transportType,
		pickupDate,
		deliveryDate,
		rateData,
		avgNumeric.RatePerTrip,
		avgNumeric.LowPerTrip,
		avgNumeric.HighPerTrip,
		"",
		"",
		models.DATSource,
		helpers.Ternary(len(stops) > 2, models.DATMultiStopLegToLeg, models.DATSpotType),
		&models.QuoteDATMetadata{
			DATTimeframe:       laneResp.Timeframe,
			DATOriginName:      laneResp.OriginName,
			DATOriginType:      laneResp.OriginType,
			DATDestinationName: laneResp.DestinationName,
			DATDestinationType: laneResp.DestinationType,
		},
	)
}

// buildSpotQuote constructs a single-leg DAT spot quote.
func buildSpotQuote(
	agg *trident.LaneRateNumerical,
	timeframe,
	originName,
	originType,
	destinationName,
	destinationType string,
	reports,
	companies int64,
) Quote {

	return Quote{
		Source: models.DATSource,
		Type:   models.DATSpotType,
		Rates: RateValues{
			Target:        agg.RatePerTrip,
			Low:           agg.LowPerTrip,
			High:          agg.HighPerTrip,
			TargetPerMile: agg.RatePerMile,
			LowPerMile:    agg.LowPerMile,
			HighPerMile:   agg.HighPerMile,
		},
		Distance: agg.Mileage,
		Metadata: map[string]any{
			"timeframe":            timeframe,
			"originName":           originName,
			"originType":           originType,
			"destinationName":      destinationName,
			"destinationType":      destinationType,
			"reports":              reports,
			"companies":            companies,
			"fuelSurchargePerTrip": agg.AvgFuelPerTrip,
			"fuelSurchargePerMile": helpers.Ternary(
				agg.Mileage != 0,
				agg.AvgFuelPerTrip/agg.Mileage,
				0.0,
			),
		},
	}
}

// buildLegToLegQuote constructs a multi-stop leg-to-leg DAT quote with per-leg metadata.
func buildLegToLegQuote(
	agg *trident.LaneRateNumerical,
	laneResp *trident.LaneRateResponse,
	reports, companies int64,
	legs []RouteLeg,
) Quote {

	return Quote{
		Source: models.DATSource,
		Type:   models.DATMultiStopLegToLeg,
		Rates: RateValues{
			Target:        agg.RatePerTrip,
			Low:           agg.LowPerTrip,
			High:          agg.HighPerTrip,
			TargetPerMile: agg.RatePerMile,
			LowPerMile:    agg.LowPerMile,
			HighPerMile:   agg.HighPerMile,
		},
		Distance: agg.Mileage,
		Metadata: map[string]any{
			"reports":              reports,
			"companies":            companies,
			"fuelSurchargePerTrip": agg.AvgFuelPerTrip,
			"fuelSurchargePerMile": helpers.Ternary(
				agg.Mileage != 0,
				agg.AvgFuelPerTrip/agg.Mileage,
				0.0,
			),
			"timeframe":       laneResp.Timeframe,
			"originName":      laneResp.OriginName,
			"originType":      laneResp.OriginType,
			"destinationName": laneResp.DestinationName,
			"destinationType": laneResp.DestinationType,
			"legs":            legs,
		},
	}
}

// buildLongestLegQuote attempts to build a multi-stop longest-leg variant.
// On error it returns nil and the caller may choose to log and continue.
func buildLongestLegQuote(
	ctx context.Context,
	user models.User,
	service models.Service,
	stops []models.Stop,
	transportType models.TransportType,
	aggNumeric *trident.LaneRateNumerical,
) (*Quote, error) {

	integration, err := integrationDB.GetByServiceIDAndType(ctx, service.ID, models.CustomerType)
	if err != nil {
		return nil, fmt.Errorf("integration lookup failed for longest-leg: %w", err)
	}

	client, err := trident.New(ctx, integration)
	if err != nil {
		return nil, fmt.Errorf("create trident client failed for longest-leg: %w", err)
	}

	if len(stops) < 2 {
		return nil, errors.New("at least two stops are required")
	}

	start := stops[0]
	end := stops[len(stops)-1]
	resp, err := client.GetLaneRate(ctx, trident.LaneRateRequest{
		OriginZip:      start.Address.Zip,
		DestinationZip: end.Address.Zip,
		Equipment:      string(transportType),
	})
	if err != nil {
		return nil, fmt.Errorf("trident first-to-last request failed for longest-leg: %w", err)
	}

	numeric, err := trident.ParseCalculateLaneRate(ctx, resp)
	if err != nil {
		return nil, fmt.Errorf("trident first-to-last parse failed for longest-leg: %w", err)
	}

	// Apply per-intermediate-stop fees
	countIntermediateStops := len(stops) - 2
	stopFeeUSD := IntermediateStopFeeUSD

	if user.IntermediateStopFeeUSD != nil {
		stopFeeUSD = *user.IntermediateStopFeeUSD
	}

	// Adjust totals
	numeric.LowPerTrip += float64(countIntermediateStops * stopFeeUSD)
	numeric.RatePerTrip += float64(countIntermediateStops * stopFeeUSD)
	numeric.HighPerTrip += float64(countIntermediateStops * stopFeeUSD)

	// Recompute per-mile using first-to-last mileage
	if numeric.Mileage > 0 {
		numeric.LowPerMile = numeric.LowPerTrip / numeric.Mileage
		numeric.RatePerMile = numeric.RatePerTrip / numeric.Mileage
		numeric.HighPerMile = numeric.HighPerTrip / numeric.Mileage
	}

	firstToLastCompanies, err := resp.Companies.Int64()
	if err != nil {
		log.Warn(ctx, "error converting Olympus company count to int64 (first-to-last)", zap.Error(err))
		firstToLastCompanies = 0
	}

	firstToLastReports, err := resp.Reports.Int64()
	if err != nil {
		log.Warn(ctx, "error converting Olympus report count to int64 (first-to-last)", zap.Error(err))
		firstToLastReports = 0
	}

	quote := Quote{
		Source: models.DATSource,
		Type:   models.DATMultiStopLongestLeg,
		Rates: RateValues{
			Target:        numeric.RatePerTrip,
			Low:           numeric.LowPerTrip,
			High:          numeric.HighPerTrip,
			TargetPerMile: numeric.RatePerMile,
			LowPerMile:    numeric.LowPerMile,
			HighPerMile:   numeric.HighPerMile,
		},
		// Keep distance comparable with leg-to-leg by using summed legs distance
		Distance: aggNumeric.Mileage,
		Metadata: map[string]any{
			"timeframe":            resp.Timeframe,
			"originName":           resp.OriginName,
			"originType":           resp.OriginType,
			"destinationName":      resp.DestinationName,
			"destinationType":      resp.DestinationType,
			"reports":              firstToLastReports,
			"companies":            firstToLastCompanies,
			"fuelSurchargePerTrip": numeric.AvgFuelPerTrip,
			"fuelSurchargePerMile": helpers.Ternary(
				numeric.Mileage != 0,
				numeric.AvgFuelPerTrip/numeric.Mileage,
				0.0,
			),
			"stopFeeUSD": stopFeeUSD,
			// TODO: deprecate when users are on latest Vulcan version
			// Only `stopFeeUSDMedium` here is used in older versions for explaining the math. We changed
			// the values here to reflect the new math that only leverages one stop fee instead of three
			// tiers.
			"stopFeeUSDLow":    stopFeeUSD,
			"stopFeeUSDMedium": stopFeeUSD,
			"stopFeeUSDHigh":   stopFeeUSD,
		},
	}

	return &quote, nil
}
