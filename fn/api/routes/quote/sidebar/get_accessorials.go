package quoteprivate

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/integrations/pricing"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

func GetLTLAccessorials(c *fiber.Ctx) error {
	ctx := log.With(c.UserContext())
	userServiceID := middleware.ServiceIDFromContext(c)
	email := middleware.ClaimsFromContext(c).Email

	user, err := rds.GetUserByEmail(ctx, email)
	if err != nil {
		log.Error(ctx, "failed to get user by email", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if userServiceID != user.ServiceID {
		log.Infof(
			ctx,
			"claims serviceID %d does not match user service ID %d",
			userServiceID,
			user.ServiceID,
		)

		return c.SendStatus(http.StatusUnauthorized)
	}

	service, err := rds.GetServiceByID(ctx, user.ServiceID)
	if err != nil {
		log.Error(ctx, "failed to get service by id", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	pricingIntegrations, err := integrationDB.GetPricingListByServiceID(ctx, service.ID)
	if err != nil {
		log.Error(ctx, "error fetching pricing integration", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var taiIntegration models.Integration
	for _, integration := range pricingIntegrations {
		if integration.Name == models.TaiPricing {
			taiIntegration = integration
			break
		}
	}

	if taiIntegration.ID == 0 {
		log.Error(ctx, "no Tai pricing integration found for service")
		return c.SendStatus(http.StatusNotFound)
	}

	client, err := pricing.New(ctx, taiIntegration, models.WithUserEmail(email))
	if err != nil {
		log.Error(ctx, "failed to create pricing client", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	resp := client.GetStaticCustomerAccessorials()
	return c.Status(http.StatusOK).JSON(resp)
}
