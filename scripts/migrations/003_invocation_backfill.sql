-- Historical Invocation Backfill
-- Splits existing PricingLookupTaskInvocation records into BuyRateLookup + SellRateCalculation invocations
-- Must run AFTER split_pricing_task.sql
-- 
-- SAFETY FEATURES:
-- - Idempotent: Can be run multiple times safely
-- - Transactional: All changes in one transaction, will rollback on error
-- - Validates data before conversion
-- - Uses COALESCE for null-safe conversions

BEGIN;

-- Reset the sequence to match the highest existing ID
SELECT setval(
    'quick_quote_agent_task_invocations_id_seq',
    (SELECT MAX(id) FROM quick_quote_agent_task_invocations) + 1
);

-- Step 1: Update step numbers in historical invocations for non-pricingLookup tasks
-- Idempotent: Only updates if action is not already buyRateLookup/sellRateCalculation
UPDATE quick_quote_agent_task_invocations ti
SET step = step + 1
WHERE action NOT IN ('pricingLookup', 'buyRateLookup', 'sellRateCalculation')
AND step < 100  -- Safety check to prevent accidental step inflation
AND EXISTS (
    SELECT 1 FROM quick_quote_agent_tasks t 
    WHERE t.id = ti.agent_task_id
);

-- Step 2: Create sellRateCalculation invocation records from pricingLookup invocations
-- Idempotent: Uses ON CONFLICT to skip if record already exists
-- Only processes records with complete metadata (non-null, positive rates)
INSERT INTO quick_quote_agent_task_invocations (
    service_id,
    user_id,
    user_email_address,
    quick_quote_agent_invocation_id,
    quick_quote_agent_id,
    agent_task_id,
    step,
    action,
    started_at,
    completed_at,
    duration_seconds,
    status,
    retries,
    error,
    braintrust_span_id,
    metadata,
    created_at,
    updated_at
)
SELECT 
    ti.service_id,
    ti.user_id,
    ti.user_email_address,
    ti.quick_quote_agent_invocation_id,
    ti.quick_quote_agent_id,
    -- Find the new sellRateCalculation task for this agent
    (SELECT t.id FROM quick_quote_agent_tasks t 
     WHERE t.quick_quote_agent_id = ti.quick_quote_agent_id 
     AND t.action = 'sellRateCalculation' LIMIT 1) AS agent_task_id,
    1 AS step,
    'sellRateCalculation' AS action,
    ti.started_at,
    ti.completed_at,
    0 AS duration_seconds,  -- Sub-millisecond for calculation
    ti.status,
    0 AS retries,
    ti.error,
    ti.braintrust_span_id || '_sell' AS braintrust_span_id,
    jsonb_build_object(
        'sellRateCalculationTaskInvocation', jsonb_build_object(
            'buyRate', COALESCE((ti.metadata->'pricingLookupTaskInvocation'->>'buyRate')::float, 
                               (ti.metadata->'pricingLookupTaskInvocation'->>'originalRate')::float),
            'sellRate', (ti.metadata->'pricingLookupTaskInvocation'->>'finalRate')::float,
            'marginType', ti.metadata->'pricingLookupTaskInvocation'->>'marginType',
            'marginValue', (ti.metadata->'pricingLookupTaskInvocation'->>'marginValue')::float,
            'pricingFormula', ti.metadata->'pricingLookupTaskInvocation'->>'pricingFormula'
        )
    ) AS metadata,
    NOW() AS created_at,
    NOW() AS updated_at
FROM quick_quote_agent_task_invocations ti
WHERE ti.action = 'pricingLookup'
AND ti.metadata->'pricingLookupTaskInvocation' IS NOT NULL
-- Data quality filter: Only convert records with valid rate data
AND COALESCE((ti.metadata->'pricingLookupTaskInvocation'->>'buyRate')::float, 
             (ti.metadata->'pricingLookupTaskInvocation'->>'originalRate')::float) > 0
AND (ti.metadata->'pricingLookupTaskInvocation'->>'finalRate')::float > 0
AND NOT EXISTS (
    -- Safety: Don't create duplicates if already exists
    SELECT 1 FROM quick_quote_agent_task_invocations existing
    WHERE existing.quick_quote_agent_invocation_id = ti.quick_quote_agent_invocation_id
    AND existing.action = 'sellRateCalculation'
);

-- Step 3: Convert pricingLookup invocations to buyRateLookup
-- Idempotent: Only updates records that are still 'pricingLookup'
-- Only processes records with valid rate data (positive buyRate)
UPDATE quick_quote_agent_task_invocations
SET 
    action = 'buyRateLookup',
    metadata = jsonb_build_object(
        'buyRateLookupTaskInvocation', jsonb_build_object(
            'selectedQuoteSource', metadata->'pricingLookupTaskInvocation'->>'selectedQuoteSource',
            'transportType', metadata->'pricingLookupTaskInvocation'->>'transportType',
            'buyRate', COALESCE((metadata->'pricingLookupTaskInvocation'->>'buyRate')::float,
                               (metadata->'pricingLookupTaskInvocation'->>'originalRate')::float),
            'distance', COALESCE((metadata->'pricingLookupTaskInvocation'->>'distance')::float, 0),
            'numQuotesReceived', COALESCE((metadata->'pricingLookupTaskInvocation'->>'numQuotesReceived')::int, 0),
            'numQuoteErrors', COALESCE((metadata->'pricingLookupTaskInvocation'->>'numQuoteErrors')::int, 0)
        )
    ),
    updated_at = NOW()
WHERE action = 'pricingLookup'
-- Data quality filter: Only convert records with valid buy rate
AND COALESCE((metadata->'pricingLookupTaskInvocation'->>'buyRate')::float,
             (metadata->'pricingLookupTaskInvocation'->>'originalRate')::float) > 0;

COMMIT;

-- =====================================================================
-- VERIFICATION QUERIES
-- Run these after the migration to ensure data integrity
-- =====================================================================

-- 1. Verify no pricingLookup invocations remain
SELECT 
    'Remaining pricingLookup invocations (should be 0)' AS check_name,
    COUNT(*) AS count
FROM quick_quote_agent_task_invocations 
WHERE action = 'pricingLookup';

-- 2. Verify all buyRateLookup invocations have valid buyRate
SELECT 
    'BuyRateLookup invocations with NULL buyRate (should be 0)' AS check_name,
    COUNT(*) AS count
FROM quick_quote_agent_task_invocations 
WHERE action = 'buyRateLookup'
AND (metadata->'buyRateLookupTaskInvocation'->>'buyRate') IS NULL;

-- 3. Verify all buyRateLookup invocations have positive buyRate
SELECT 
    'BuyRateLookup invocations with invalid buyRate (should be 0)' AS check_name,
    COUNT(*) AS count
FROM quick_quote_agent_task_invocations 
WHERE action = 'buyRateLookup'
AND COALESCE((metadata->'buyRateLookupTaskInvocation'->>'buyRate')::float, 0) <= 0;

-- 4. Verify all sellRateCalculation invocations have valid buyRate and sellRate
SELECT 
    'SellRateCalculation invocations with invalid rates (should be 0)' AS check_name,
    COUNT(*) AS count
FROM quick_quote_agent_task_invocations 
WHERE action = 'sellRateCalculation'
AND (
    (metadata->'sellRateCalculationTaskInvocation'->>'buyRate') IS NULL
    OR (metadata->'sellRateCalculationTaskInvocation'->>'sellRate') IS NULL
    OR COALESCE((metadata->'sellRateCalculationTaskInvocation'->>'buyRate')::float, 0) <= 0
    OR COALESCE((metadata->'sellRateCalculationTaskInvocation'->>'sellRate')::float, 0) <= 0
);

-- 5. Verify action pairing per invocation
SELECT 
    'Invocation actions per agent (sample of first 10)' AS check_name,
    quick_quote_agent_id,
    quick_quote_agent_invocation_id,
    array_agg(action ORDER BY step) AS actions
FROM quick_quote_agent_task_invocations 
GROUP BY quick_quote_agent_id, quick_quote_agent_invocation_id
LIMIT 10;

-- 6. Count of each action type after migration
SELECT 
    'Action type distribution' AS check_name,
    action,
    COUNT(*) AS count
FROM quick_quote_agent_task_invocations
GROUP BY action
ORDER BY action;

-- 7. Verify step ordering is correct
SELECT 
    'Invocations with incorrect step ordering (should be 0)' AS check_name,
    COUNT(DISTINCT quick_quote_agent_invocation_id) AS count
FROM (
    SELECT 
        quick_quote_agent_invocation_id,
        step,
        LAG(step) OVER (PARTITION BY quick_quote_agent_invocation_id ORDER BY step) as prev_step
    FROM quick_quote_agent_task_invocations
) sub
WHERE prev_step IS NOT NULL AND step != prev_step + 1;

-- 8. Count of pricingLookup records that were NOT converted (due to invalid data)
SELECT 
    'PricingLookup records skipped due to invalid data' AS check_name,
    COUNT(*) AS count
FROM quick_quote_agent_task_invocations
WHERE action = 'pricingLookup'
AND (
    metadata->'pricingLookupTaskInvocation' IS NULL
    OR COALESCE((metadata->'pricingLookupTaskInvocation'->>'buyRate')::float,
                (metadata->'pricingLookupTaskInvocation'->>'originalRate')::float) IS NULL
    OR COALESCE((metadata->'pricingLookupTaskInvocation'->>'buyRate')::float,
                (metadata->'pricingLookupTaskInvocation'->>'originalRate')::float) <= 0
);

-- 9. Sample of skipped records (for debugging)
SELECT 
    'Sample of skipped pricingLookup records' AS info,
    id,
    quick_quote_agent_invocation_id,
    status,
    created_at,
    metadata->'pricingLookupTaskInvocation' as pricing_metadata
FROM quick_quote_agent_task_invocations
WHERE action = 'pricingLookup'
AND (
    metadata->'pricingLookupTaskInvocation' IS NULL
    OR COALESCE((metadata->'pricingLookupTaskInvocation'->>'buyRate')::float,
                (metadata->'pricingLookupTaskInvocation'->>'originalRate')::float) <= 0
)
LIMIT 5;
