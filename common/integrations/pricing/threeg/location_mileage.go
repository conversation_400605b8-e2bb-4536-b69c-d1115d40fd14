package threeg

import (
	"context"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
)

// GetMileageByZipCodes returns the mileage between origin and destination.
func (c *Client) GetMileageByZipCodes(
	ctx context.Context,
	customerOrganizationID,
	pcmilerRoutingType int,
	origin,
	destination models.Address,
) (float64, error) {
	return 0, helpers.NotImplemented(models.ThreeG, "GetMileageByZipCodes")
}
