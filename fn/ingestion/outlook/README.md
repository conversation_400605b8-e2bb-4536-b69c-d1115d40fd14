# Outlook Ingestion Lambda

A lambda function for getting emails via Outlook webhooks and our native [Microsoft Graph SDK](../../../common/integrations/email/msclient/ms.go).

# Resources

View the in-depth tutorial, which includes information on infrastructure, customer onboarding, permissions, etc, on [Notion](https://www.notion.so/axleapi/Beacon-Outlook-pt-2-Auth-Ingestion-885bc6be5e82478ea21c461af5ccc577). This README focuses on how to run the Outlook ingestion lambda locally.

# Testing Webhooks

## Manually Send Webhooks

1. Open a terminal in this directory and run `go run .`
2. Use [Postman](https://axleapi.postman.co/workspace/5e2b164c-2177-4654-b2b9-59016998cbae) or your preferred tool to send a webhook to http://localhost:5006/inboxWebhook. You can search the Cloudwatch logs for an example, or see example [here](https://axleapi.postman.co/workspace/5e2b164c-2177-4654-b2b9-59016998cbae/request/22691706-875ce3ac-aa7f-4934-bfaa-b78f87b987ab?tab=body).

   > Callout: The user ID, client state and subscription ID must match what's in the DB.

   > Callout: By default, ingestion lambdas skip duplicate messages to prevent getting rate limited in production. Comment this out if you're testing something.

## Receive Actual Webhooks from Outlook Locally

Helpful for testing end-to-end processing flows, particularly categorization and AI parsing.

1. Open a terminal in this directory and run `go run .`.
1. In a separate terminal, start ngrok and automatically update the API's `.env` file:
   1. **Recommended**: Use the convenience script: `./scripts/dev/start-ngrok.sh`
      - This script starts ngrok on port 5006 (the default port for Outlook ingestion in dev)
      - Automatically detects the ngrok public URL
      - Updates `fn/api/.env` with `MICROSOFT_WEBHOOK_URL={ngrokURL}/inboxWebhook`
      - **Note**: Before running, update `DRUMKIT_ROOT` in the script to point to your project root directory
   1. **Manual**: Run `ngrok http 5006` (the port that Outlook ingestion runs on in dev).
      - For more information on ngrok, see [here](https://ngrok.com/docs/getting-started/)
      - ngrok returns a public URL assigned to this port. In the [API's env file](../../api/.env), add `MICROSOFT_WEBHOOK_URL={ngrokURL}/inboxWebhook` (note the `/inboxWebhook` path is required).
1. Run the API, Processor, Portal locally.
1. Sign into Drumkit with Microsoft SSO in 1 of 2 ways:
   1. Go to local Drumkit Portal's signup page (http://localhost:5173/signup), click Microsoft then follow the prompts.
   1. From within Outlook inbox, [sideload the add-in](https://support.microsoft.com/en-us/office/using-add-ins-in-outlook-on-the-web-8f2ce816-5df4-44a5-958c-f7f9d6dabdce#:~:text=Add%20custom%20add%2Dins%20from%20a%20file), open the add-in sidebar, then click "Sign into Beacon".
   1. View [Vesta README](https://github.com/drumkitai/vesta) for more information.
1. Now, Outlook will send email webhooks to the ngrok URL which will be piped over to your local ingestion and processor lambda.

# Additional Notes

## Tests

You should write tests as much as possible. See [main_test.go](./main_test.go) for examples of end-to-end unit tests using mocks for external dependencies like the DB.

## Backfills

When a new user signs-up, their first email webhook will trigger a 5-day backfill of their emails. If you need to manually trigger a backfill (for example, due to the Great Aljex IP Address Incident of 2024), do the following:

1. (Optional) If you need to trigger re-processing of messages already in the DB (such as, if there was a bug associating loads with emails or generating AI suggestions), set the `SKIP_DUPLICATES=false` in the [Outlook ingestion](https://us-east-1.console.aws.amazon.com/lambda/home?region=us-east-1#/functions/beacon-ingestion-outlook?tab=code) AND [Processor](https://us-east-1.console.aws.amazon.com/lambda/home?region=us-east-1#/functions/beacon-processor?tab=code) lambdas (Lambda console -> beacon-ingestion-outlook/beacon-processor -> Configuration tab -> Environment variables).
   - **Context:** As an optimization, for some AI suggestions (like load building) we don't create multiple suggestions for a thread if one already exists as users may continue to use the same thread for all communications related to that shipment. But there are cases where we need to backfill/correct an existing suggestion, hence the `SKIP_DUPLICATES=false` flag.
1. In the DB, identify the target user and set `updated_at = created_at` in a SQL transaction (Postico and Tableplus(?) automatically perform updates in transactions). This is the condition Outlook ingestion Lambda checks to know it should backfill.
1. Trigger backfill by either:
   1. Waiting for the Outlook ingestion Lambda to receive a webhook for that user. When complete, the `updated_at` will no longer equal `created_at`.
   1. Another option is to manually trigger ingestion by replaying the most recent webhook. Search the ingestion logs for it, then use Lambda console's [Test](https://us-east-1.console.aws.amazon.com/lambda/home?region=us-east-1#/functions/beacon-ingestion-outlook?tab=testing) feature or Postman to re-POST that webhook.
1. If you did step 1, be sure to reset `SKIP_DUPLICATES=true` in BOTH ingestion & processor lambdas when the backfill is complete.
