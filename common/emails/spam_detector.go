package emails

import (
	"regexp"
	"strings"
	"sync"

	"github.com/drumkitai/drumkit/common/models"
)

// SpamDetectionReason indicates why an email was classified as spam
type SpamDetectionReason string

const (
	ReasonOutOfOffice      SpamDetectionReason = "out_of_office_auto_reply"
	ReasonBounceOrNDR      SpamDetectionReason = "bounce_or_ndr"
	ReasonSubjectBlacklist SpamDetectionReason = "subject_blacklist"
	ReasonBodyBlacklist    SpamDetectionReason = "body_blacklist"
)

// BlacklistPattern represents an exact match or substring pattern.
type BlacklistPattern struct {
	// patternLower is the pattern converted to lowercase for fast matching.
	patternLower string
	// exact is true for an exact match, false for a substring match.
	exact bool
}

// NewBlacklistPattern creates a pattern with its lowercase version pre-calculated.
func NewBlacklistPattern(pattern string, exact bool) BlacklistPattern {
	return BlacklistPattern{
		patternLower: strings.ToLower(pattern),
		exact:        exact,
	}
}

// SpamDetector detects spam and auto-generated emails.
type SpamDetector struct {
	// subjectBlacklist contains specific subject line patterns.
	subjectBlacklist []BlacklistPattern
	// bodyBlacklist contains specific email body patterns.
	bodyBlacklist []BlacklistPattern
}

var (
	ndrPatternsOnce sync.Once
	ndrPatterns     []BlacklistPattern
)

// getNDRPatterns returns the NDR patterns, ensuring they are initialized only once.
func getNDRPatterns() []BlacklistPattern {
	ndrPatternsOnce.Do(func() {
		ndrPatterns = buildNDRBlacklist()
	})
	return ndrPatterns
}

var (
	oooPatternsOnce sync.Once
	oooPatterns     []BlacklistPattern
)

// getOOOPatterns returns the OOO patterns, ensuring they are initialized only once.
func getOOOPatterns() []BlacklistPattern {
	oooPatternsOnce.Do(func() {
		oooPatterns = buildOOOBlacklist()
	})
	return oooPatterns
}

// NewSpamDetector creates a new spam detector instance.
func NewSpamDetector() *SpamDetector {
	return &SpamDetector{
		subjectBlacklist: buildSubjectBlacklist(),
		bodyBlacklist:    buildBodyBlacklist(),
	}
}

// IsSpam checks if an email is spam or auto-generated.
func (sd *SpamDetector) IsSpam(email models.IngestedEmail) (bool, SpamDetectionReason) {
	// Check for bounce/NDR indicators (high confidence, delivery failures)
	if sd.matchesBlacklistLower(strings.ToLower(email.Subject), getNDRPatterns()) {
		return true, ReasonBounceOrNDR
	}

	// Check for out-of-office auto-replies (high confidence)
	if sd.checkOutOfOfficeReplies(email) {
		return true, ReasonOutOfOffice
	}

	// Check subject blacklist for known problematic patterns
	if sd.matchesBlacklistLower(strings.ToLower(email.Subject), sd.subjectBlacklist) {
		return true, ReasonSubjectBlacklist
	}

	// Check body blacklist for known problematic patterns
	if sd.matchesBlacklistLower(strings.ToLower(email.Body), sd.bodyBlacklist) {
		return true, ReasonBodyBlacklist
	}

	return false, ""
}

// buildSubjectBlacklist returns a list of subject line patterns.
func buildSubjectBlacklist() []BlacklistPattern {
	return []BlacklistPattern{
		// Exact matches (high confidence)
		NewBlacklistPattern("Microsoft 365 security: You have messages in quarantine", true),
		NewBlacklistPattern("Develop Key Leadership Skills in Our New Manager Bootcamp", true),
		NewBlacklistPattern("Creating Effective DEIB Initiatives", true),
		NewBlacklistPattern("Proofpoint Essentials - Quarantine Digest", true),
		NewBlacklistPattern("Brokerage Operations Report by Revenue Code", true),
		NewBlacklistPattern("No contact center agents on duty", true),
		NewBlacklistPattern("Account Verification", true),
		NewBlacklistPattern("Your archive mailbox is full", true),
		NewBlacklistPattern("Contact center hold queue size has increased", true),
		NewBlacklistPattern("Breakthrough Fuel Daily Fuel Recovery Report", true),
		NewBlacklistPattern("Your Highway verification code", true),
		NewBlacklistPattern("Your call transcript is now available", true),
		NewBlacklistPattern("Highway Product Updates This Week", true),
		NewBlacklistPattern("Monthly Broker Revenue Report", true),
		NewBlacklistPattern("Coro Notification: Spam", true),

		// Substring patterns
		NewBlacklistPattern("DAT broker newsletter:", false),
		NewBlacklistPattern("System Incident Report:", false),
		NewBlacklistPattern("spam report", false),
		NewBlacklistPattern("Shipment Status Errors for", false),
		NewBlacklistPattern("Quarantine List - ", false),
	}
}

// buildBodyBlacklist returns a list of email body patterns.
func buildBodyBlacklist() []BlacklistPattern {
	return []BlacklistPattern{
		// TODO: Add blacklist patterns from Sentry/CloudWatch
		// Example exact match:
		// NewBlacklistPattern("specific problematic text", true),
		// Example substring pattern:
		// NewBlacklistPattern("phishing attempt phrase", false),
	}
}

// buildNDRBlacklist returns NDR/bounce detection patterns.
func buildNDRBlacklist() []BlacklistPattern {
	return []BlacklistPattern{
		// Very specific bounce/NDR patterns
		NewBlacklistPattern("delivery failed", false),
		NewBlacklistPattern("delivery status notification", false),
		NewBlacklistPattern("mail delivery failure", false),
		NewBlacklistPattern("undeliverable", false),
		NewBlacklistPattern("undelivered mail", false),
		NewBlacklistPattern("returned mail", false),
		NewBlacklistPattern("non-delivery", false),
		NewBlacklistPattern("failure notice", false),
		NewBlacklistPattern("mailer-daemon", false),
		NewBlacklistPattern("mail delivery failed", false),
		NewBlacklistPattern("permanent failure", false),
		NewBlacklistPattern("temporary failure", false),
		NewBlacklistPattern("delivery error", false),
		NewBlacklistPattern("mail system error", false),
		NewBlacklistPattern("delivery notification", false),
		NewBlacklistPattern("failure delivery notification", false),
		NewBlacklistPattern("message delivery failed", false),
		NewBlacklistPattern("message could not be sent", false),

		// Forwarded bounce messages
		NewBlacklistPattern("fw: delivery failed", false),
		NewBlacklistPattern("fw: failure to deliver", false),
		NewBlacklistPattern("fw: undeliverable", false),
		NewBlacklistPattern("fwd: delivery failed", false),
		NewBlacklistPattern("fwd: failure to deliver", false),
		NewBlacklistPattern("fwd: undeliverable", false),

		// Failures with "RE:" or "Re:" prefix
		NewBlacklistPattern("re: delivery failed", true),
		NewBlacklistPattern("re: undeliverable", true),
		NewBlacklistPattern("re: failure to deliver", true),

		// Single word/phrase exact matches (high confidence)
		NewBlacklistPattern("bounce", true),
		NewBlacklistPattern("ndr", true),
		NewBlacklistPattern("rejected", true),
		NewBlacklistPattern("unable to deliver", true),
	}
}

// buildOOOBlacklist returns out-of-office patterns.
func buildOOOBlacklist() []BlacklistPattern {
	return []BlacklistPattern{
		NewBlacklistPattern("out of office", false),
		NewBlacklistPattern("out-of-office", false),
		NewBlacklistPattern("i am out of the office", false),
		NewBlacklistPattern("i am away", false),
		NewBlacklistPattern("away from my desk", false),
		NewBlacklistPattern("automatic reply", false),
		NewBlacklistPattern("auto-reply", false),
		NewBlacklistPattern("autoreply", false),
	}
}

// matchesBlacklistLower checks if the given lowercased text matches any patterns.
// The text must be lowercased by the caller.
func (sd *SpamDetector) matchesBlacklistLower(textLower string, patterns []BlacklistPattern) bool {
	for _, bp := range patterns {
		if bp.exact {
			if textLower == bp.patternLower {
				return true
			}
		} else {
			if strings.Contains(textLower, bp.patternLower) {
				return true
			}
		}
	}
	return false
}

// hasLoadBuildingIndicators checks if an email has indicators that suggest it's a load building email.
// If so, we should be more lenient with spam detection to avoid false positives.
// Cancel bypass if subject contains strong OOO indicators ("Out of Office" or "Automatic Reply").
func hasLoadBuildingIndicators(email models.IngestedEmail) bool {
	subjectLower := strings.ToLower(email.Subject)
	strongOOOPatterns := []BlacklistPattern{
		NewBlacklistPattern("out of office", false),
		NewBlacklistPattern("out-of-office", false),
		NewBlacklistPattern("automatic reply", false),
		NewBlacklistPattern("auto-reply", false),
		NewBlacklistPattern("autoreply", false),
	}

	// If subject contains OOO indicators, don't bypass spam filter
	tempDetector := &SpamDetector{}
	if tempDetector.matchesBlacklistLower(subjectLower, strongOOOPatterns) {
		return false
	}

	// Check attachment filenames for specific load building indicators
	// Pattern matches "bol" at word boundary followed by non-letter (underscore, digit, etc.) or at end of string
	// This catches patterns like bol_12345.pdf, bol123456.pdf, bol_document.pdf, bol.pdf
	bolPattern := regexp.MustCompile(`\bbol[^a-z]|bol$`)
	for _, att := range email.Attachments {
		fileNameLower := strings.ToLower(att.OriginalFileName)
		if bolPattern.MatchString(fileNameLower) ||
			strings.Contains(fileNameLower, "bill of lading") ||
			strings.Contains(fileNameLower, "pickup") ||
			strings.Contains(fileNameLower, "delivery") {
			return true
		}
	}

	return false
}

// checkOutOfOfficeReplies checks if the email is an out-of-office auto-reply.
func (sd *SpamDetector) checkOutOfOfficeReplies(email models.IngestedEmail) bool {
	// Skip OOO check for emails with load building indicators to avoid false positives
	if hasLoadBuildingIndicators(email) {
		return false
	}

	subjectLower := strings.ToLower(email.Subject)
	bodyLower := strings.ToLower(email.Body)

	oooPatterns := getOOOPatterns()

	// Check subject for OOO indicators
	if sd.matchesBlacklistLower(subjectLower, oooPatterns) {
		return true
	}

	// Check body for OOO indicators
	if sd.matchesBlacklistLower(bodyLower, oooPatterns) {
		return true
	}

	return false
}
