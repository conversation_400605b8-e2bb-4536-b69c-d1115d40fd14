package threeg

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

const redisKeyTTL = 30 * time.Minute

func getKey(email string) string {
	return fmt.Sprintf("threeg-quoting-email-%s", strings.ToLower(email))
}

func getCachedClient(ctx context.Context, userName string) *Client {
	c, found, err := redis.GetKey[Client](ctx, getKey(userName))
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "failed to get ThreeG client from Redis", zap.Error(err))
	}

	if found {
		log.Info(ctx, "re-using existing ThreeG client")
		c.HTTPClient = &http.Client{}
		return &c
	}

	return nil
}

func (c *Client) cache(ctx context.Context) error {
	key := get<PERSON>ey(c.User<PERSON>)

	err := redis.SetKey(ctx, key, c, redisKeyTTL)
	if err != nil {
		return errors.New("failed to cache ThreeG client")
	}

	return nil
}
