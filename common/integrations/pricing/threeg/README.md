# ThreeG package

A client for ThreeG that:
- Authenticates and captures response cookies
- Reuses cookies for subsequent requests
- Caches the authenticated client + cookies in Redis per user email
- Provides helpers for:
  - Customer typeahead search
  - Listing customer staff
  - Fetching customer accessorials
  - Getting mileage by zip codes
  - Getting LTL quick quotes (v2)

## Auth, cookies, and caching

- New(ctx, integration, userEmail) authenticates if no cached client
  exists for the given userEmail.
- On successful login, the Set-Cookie values are stored on Client.Cookies.
- Client is cached in Redis with a 30-minute TTL (key:
  threeg-email-<lowercased-userEmail>).
- Subsequent requests add the cookies via http.Request.AddCookie.
- Integration (credentials) is not persisted to Red<PERSON> (json:"-"). When a
  cached client is loaded, <PERSON> re-applies the provided Integration.

## Quick start

```go
client, err := threeG.New(ctx, integration, "<EMAIL>")
if err != nil {
  // handle error
}
```
