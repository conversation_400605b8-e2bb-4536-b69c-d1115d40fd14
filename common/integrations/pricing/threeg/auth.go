package threeg

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"strings"
	"time"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"golang.org/x/net/publicsuffix"
)

const loginEndpoint = "/web/resources/j_spring_security_check"

// ensureHTTPClient guarantees an http.Client with cookie jar.
func (c *Client) ensureHTTPClient() error {
	if c.HTTPClient != nil && c.HTTPClient.Jar != nil {
		return nil
	}

	jar, err := cookiejar.New(&cookiejar.Options{
		PublicSuffixList: publicsuffix.List,
	})
	if err != nil {
		return fmt.Errorf("failed to create cookie jar: %w", err)
	}

	client := otel.TracingHTTPClient(60 * time.Second)
	client.Jar = jar

	c.HTTPClient = client

	return nil
}

func getHostname(tenant string) string {
	// Strip http:// or https:// if present (in case user pastes full URL)
	hostname := strings.TrimPrefix(tenant, "https://")
	hostname = strings.TrimPrefix(hostname, "http://")

	if strings.Contains(hostname, ".") {
		// Tenant is already a full hostname
		return hostname
	}
	// Legacy fallback: append .3gtms.com for backward compatibility
	// Note: This assumes all tenants use the 3gtms.com domain, which may not be true for all customers.
	// For custom domains, provide the full hostname in the tenant field.
	return fmt.Sprintf("%s.3gtms.com", hostname)
}

// login performs authentication with 3G TMS and returns the session cookie
func (t *Client) login(ctx context.Context) error {
	// Use the web base URL from the ThreeG struct (handles custom hostnames)
	endpoint := fmt.Sprintf("%s%s", t.WebBaseURL, loginEndpoint)

	// Decrypt the password
	decryptedPassword, err := crypto.DecryptAESGCM(ctx, string(t.Integration.EncryptedPassword), nil)
	if err != nil {
		return fmt.Errorf("error decrypting password: %w", err)
	}

	if err := t.ensureHTTPClient(); err != nil {
		return err
	}

	log.Debug(ctx, "ThreeG auth: HTTP client ready with cookie jar")
	// Create form data for login
	formData := url.Values{}
	formData.Set("j_username", t.Integration.Username)
	formData.Set("j_password", decryptedPassword)

	// Create the request
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, endpoint, strings.NewReader(formData.Encode()))
	if err != nil {
		return fmt.Errorf("error creating login request: %w", err)
	}

	// Set headers to match the successful curl request
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	//nolint:lll
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("DNT", "1")
	req.Header.Set("Origin", t.WebBaseURL)
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Referer", fmt.Sprintf("%s/web/login", t.WebBaseURL))
	req.Header.Set("Sec-Ch-Ua", `"Chromium";v="137", "Not/A)Brand";v="24"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"macOS"`)
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	//nolint:lll
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36")

	// Make the request
	resp, err := t.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("error making login request: %w", err)
	}
	defer resp.Body.Close()

	// Check for successful login
	if resp.StatusCode != http.StatusOK {
		// Read response body for error details
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("login failed with status: %s (could not read response body)", resp.Status)
		}
		return fmt.Errorf("login failed with status: %s - response: %s", resp.Status, string(body))
	}

	// Verify we got a session cookie
	parsedURL, err := url.Parse(endpoint)
	if err != nil {
		return fmt.Errorf("error parsing URL: %w", err)
	}

	cookies := t.HTTPClient.Jar.Cookies(parsedURL)
	if len(cookies) == 0 {
		return errors.New("no session cookie received after login")
	}

	requiredCookie := "JSESSIONID" // AWSALB and AWSALBCORS are optional
	found := false
	for _, cookie := range cookies {
		if cookie.Name == requiredCookie {
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("missing required cookie after login: %s", requiredCookie)
	}

	return nil
}
