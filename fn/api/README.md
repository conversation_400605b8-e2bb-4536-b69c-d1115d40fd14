# API Routes

## Route Requirements

### 1. Validation Testing (Ongoing Coverage)

We are incrementally increasing our validation test coverage. You should add tests when:

- Creating a new route
- Modifying an existing route
- Working in a route directory that lacks tests

This is an ongoing process - every PR is an opportunity to improve our test coverage.

Your responsibility:

- ✅ Always add tests for new routes you create
- ✅ Add tests when modifying existing routes that lack them
- ✅ Consider adding tests to nearby untested routes while you're in that area

These tests ensure that:

- Query parameter validation works correctly
- Request body validation works correctly
- Nested/embedded structs are properly validated

See the detailed testing guide: [API Validation Testing](testing/README.md)

### 2. Test File Location

Place test files next to the route handler:

```
fn/api/
├── routes/
│   ├── appt/
│   │   ├── get_slots.go
│   │   ├── get_slots_test.go      // ✅ Test next to route
│   │   ├── submit_appt.go
│   │   └── submit_appt_test.go    // ✅ Test next to route
│   └── other/
└── testing/
    └── README.md                   // Detailed testing guide
```

### 3. Why Testing is Helpful

Recent production issues have shown that:

- Changes to nested structs can break API validation
- Required vs optional fields aren't always obvious
- The Drumkit sidebar breaks when validation changes unexpectedly

Testing catches these issues during development, not in production.

### 4. PR Requirements

Pull Requests that add or modify routes should include:

- ✅ Validation tests for all query/body structs
- ✅ Tests for both valid and invalid cases
- ✅ Tests that verify required field validation

See the [testing guide](testing/README.md) for detailed instructions.

## Development Tools

### ngrok Setup for Outlook Webhooks

When testing Outlook webhooks locally, you need to expose your local Outlook ingestion service (running on port 5006) to the internet. The `scripts/dev/start-ngrok.sh` script automates this process:

- Starts ngrok on port 5006 (the default port for Outlook ingestion in dev)
- Automatically detects the ngrok public URL
- Updates `fn/api/.env` with `MICROSOFT_WEBHOOK_URL={ngrokURL}/inboxWebhook`

**Before running**: Update `DRUMKIT_ROOT` in the script to point to your project root directory.

See the [Outlook ingestion README](../ingestion/outlook/README.md) for more details on testing webhooks locally.

## Additional Notes

We have protected API routes meant for internal use that check against `INTERNAL_DRUMKIT_KEY`.

- In prod this key is in AWS Secrets Manager (and you can also view it in the shared 1Password folder)

One protected route meant to be used by Drumkit team members only is the POST /carrier-groups route. It validates the incoming request to ensure the body has the `INTERNAL_DRUMKIT_KEY` which is in the json body as
`internalDrumkitKey`.
