-- Cleanup Script (Run after migration observation period)
-- Drops backup tables and removes deprecated data
-- Only run this after verifying the migration was successful

BEGIN;

-- Drop backup tables
DROP TABLE IF EXISTS quick_quote_agent_tasks_backup;
DROP TABLE IF EXISTS quick_quote_agent_task_invocations_backup;

COMMIT;

-- Note: Do NOT remove PricingLookupAction handler code until this cleanup is complete
-- and you've verified all agents are working correctly with the new split tasks
