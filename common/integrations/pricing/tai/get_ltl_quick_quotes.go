package tai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const ltlQuickQuotesV2Endpoint = "/api/customerorder/quotesv2/"

type QuickQuoteLineItem struct {
	PackageType                      int      `json:"packageType"`
	FreightClass                     string   `json:"freightClass"`
	ClassManuallySelected            bool     `json:"classManuallySelected"`
	CubicFeet                        string   `json:"cubicFeet"`
	CubicMeters                      string   `json:"cubicMeters"`
	PoundsPerCubicFeet               string   `json:"poundsPerCubicFeet"`
	DimensionalWeight                *int     `json:"dimensionalWeight"`
	ChargeableWeight                 *int     `json:"chargeableWeight"`
	EstimatedFreightClass            string   `json:"estimatedFreightClass"`
	ShowEstimatedFreightClassMessage bool     `json:"showEstimatedFreightClassMessage"`
	CubicFeetChanged                 bool     `json:"cubicFeetChanged"`
	QuantityOfGoods                  int      `json:"quantityOfGoods"`
	TotalWeight                      int      `json:"totalWeight"`
	CommodityDescription             string   `json:"commodityDescription"`
	HandlingUnits                    int      `json:"handlingUnits"`
	SizeLength                       *float32 `json:"sizeLength"`
	SizeWidth                        *float32 `json:"sizeWidth"`
	SizeHeight                       *float32 `json:"sizeHeight"`
	NMFCNumber                       string   `json:"nmfcNumber"`
}

type LTLQuickQuoteRequest struct {
	ShipmentID                   int                  `json:"shipmentId"`
	OriginCity                   string               `json:"originCity"`
	OriginState                  string               `json:"originState"`
	OriginZip                    string               `json:"originZip"`
	OriginCountry                string               `json:"originCountry"`
	LineItems                    []QuickQuoteLineItem `json:"lineItems"`
	BrokerID                     int                  `json:"brokerId"`
	CustomerStaffID              int                  `json:"customerStaffId"`
	DestinationCity              string               `json:"destinationCity"`
	DestinationState             string               `json:"destinationState"`
	DestinationZip               string               `json:"destinationZip"`
	DestinationCountry           string               `json:"destinationCountry"`
	WeightUnit                   int                  `json:"weightUnit"`
	DimensionUnit                int                  `json:"dimensionUnit"`
	SearchByTariff               bool                 `json:"searchByTariff"`
	Stackable                    bool                 `json:"stackable"`
	ShipmentType                 int                  `json:"shipmentType"`
	ActualShipmentType           int                  `json:"actualShipmentType"`
	TrailerType                  int                  `json:"trailerType"`
	PickupReadyDateTime          string               `json:"pickupReadyDateTime"`
	PickupCloseDateTime          string               `json:"pickupCloseDateTime"`
	PickupAppointmentBeginTime   *string              `json:"pickupAppointmentBeginTime"`
	PickupAppointmentEndTime     *string              `json:"pickupAppointmentEndTime"`
	DeliveryReadyDateTime        *string              `json:"deliveryReadyDateTime"`
	DeliveryCloseDateTime        *string              `json:"deliveryCloseDateTime"`
	DeliveryAppointmentBeginTime *string              `json:"deliveryAppointmentBeginTime"`
	DeliveryAppointmentEndTime   *string              `json:"deliveryAppointmentEndTime"`
	Mileage                      float64              `json:"mileage"`
	Accessorials                 []int                `json:"accessorials"`
	IsFrontOffice                bool                 `json:"isFrontOffice"`
	DeclaredValue                string               `json:"declaredValue"`
	DeductibleValue              float64              `json:"deductibleValue"`
	LinearFeet                   *int                 `json:"linearFeet"`
}

// NOTE: Unused fields retained for potential future use.
type AccessorialPrices struct {
	Buy             float64 `json:"buy"`
	Sell            float64 `json:"sell"`
	AccessorialName string  `json:"accessorialName"`
	// TariffProfileID   int     `json:"tariffProfileId"`
	// AccessorialID     int     `json:"accessorialId"`
	// AccessorialCode   string  `json:"accessorialCode"`
	// Type              int     `json:"type"`
	// CalculationType   int     `json:"calculationType"`
	// CalculationAmount float64 `json:"calculationAmount"`
}

// NOTE: Unused fields retained for potential future use.
type QuoteMargin struct {
	// MarginType              int     `json:"marginType"`
	// MarginDetailID          int     `json:"marginDetailId"`
	// AppliesToOrganizationID int     `json:"appliesToOrganizationId"`
	// ParentID                int     `json:"parentId"`
	// MarginCalculationType   int     `json:"marginCalculationType"`
	MarkupAmount float64 `json:"markupAmount"`
	// CalculatedAmount        float64 `json:"calculatedAmount"`
	// ProcessingOrder         int     `json:"processingOrder"`
	// MarginFeeType           int     `json:"marginFeeType"`
}

//nolint:lll
type Quote struct {
	Index                                            int                 `json:"index"`
	SellRateFreightCharges                           float64             `json:"sellRateFreightCharges"`
	BaseRateFreightChargesAfterDiscountOrMinimumRate float64             `json:"baseRateFreightChargesAfterDiscountOrMinimumRate"`
	SellFuelSurcharge                                float64             `json:"sellFuelSurcharge"`
	SellFuelPercentage                               float64             `json:"sellFuelPercentage"`
	SellAccessorialTotalAmount                       float64             `json:"sellAccessorialTotalAmount"`
	SellRateTotal                                    float64             `json:"sellRateTotal"`
	BuyRateFreightCharges                            float64             `json:"buyRateFreightCharges"`
	BuyFuelSurcharge                                 float64             `json:"buyFuelSurcharge"`
	BuyFuelPercentage                                float64             `json:"buyFuelPercentage"`
	BuyAccessorialTotalAmount                        float64             `json:"buyAccessorialTotalAmount"`
	BuyRateTotal                                     float64             `json:"buyRateTotal"`
	CarrierID                                        int                 `json:"carrierId"`
	CarrierName                                      string              `json:"carrierName"`
	CarrierSCAC                                      string              `json:"carrierSCAC"`
	TsaCompliance                                    int                 `json:"tsaCompliance"`
	Days                                             int                 `json:"days"`
	ErrorMessage                                     string              `json:"errorMessage"`
	IsError                                          bool                `json:"isError"`
	IsPredictedRate                                  bool                `json:"isPredictedRate"`
	NotServiced                                      bool                `json:"notServiced"`
	ServiceLevel                                     int                 `json:"serviceLevel"`
	ServiceLevelDisplayName                          string              `json:"serviceLevelDisplayName"`
	TariffProfileID                                  int                 `json:"tariffProfileId"`
	TariffOwnerOrganizationID                        int                 `json:"tariffOwnerOrganizationId"`
	TariffLinkOrganizationID                         int                 `json:"tariffLinkOrganizationId"`
	TariffLinkID                                     int                 `json:"tariffLinkId"`
	TariffLaneID                                     int                 `json:"tariffLaneId"`
	TariffName                                       string              `json:"tariffName"`
	AccessorialPrices                                []AccessorialPrices `json:"accessorialPrices"`
	IntegrationSourceType                            int                 `json:"integrationSourceType"`
	IntegrationSourceTypeDisplayName                 string              `json:"integrationSourceTypeDisplayName"`
	TariffCalculationType                            int                 `json:"tariffCalculationType"`
	TariffComments                                   string              `json:"tariffComments"`
	Guaranteed                                       bool                `json:"guaranteed"`
	IsVolume                                         bool                `json:"isVolume"`
	SaveWithoutRate                                  bool                `json:"saveWithoutRate"`
	IsMarketPlace                                    bool                `json:"isMarketPlace"`
	RatingType                                       int                 `json:"ratingType"`
	IsContractRate                                   bool                `json:"isContractRate"`
	Margins                                          []QuoteMargin       `json:"margins"`
	TransitType                                      int                 `json:"transitType"`
}

type LTLQuickQuoteResponse struct {
	Quotes          []Quote `json:"quotes"`
	EncryptedQuotes string  `json:"encryptedQuotes"`
}

// GetLTLQuickQuotes posts the request and returns only successful quotes mapped to models.QuickQuote.
// NOTE: Failed quotes are logged (carrier + reason) but captured since they contain useful information.
func (c *Client) GetLTLQuickQuotes(
	ctx context.Context,
	req LTLQuickQuoteRequest,
) ([]models.QuickQuote, string, error) {
	normalizedReq, err := NormalizeLTLQuickQuoteLocations(ctx, req)
	if err != nil {
		return nil, "", err
	}

	payload, err := json.Marshal(normalizedReq)
	if err != nil {
		return nil, "", fmt.Errorf("failed to marshal LTL quick quote request: %w", err)
	}
	body := bytes.NewBuffer(payload)

	var resp LTLQuickQuoteResponse
	_, err = c.post(ctx, ltlQuickQuotesV2Endpoint, body, &resp)
	if err != nil {
		return nil, "", err
	}

	out := make([]models.QuickQuote, 0, len(resp.Quotes))
	for _, q := range resp.Quotes {
		if q.IsError || q.NotServiced {
			log.Info(
				ctx,
				"Tai LTL quote failure",
				zap.String("carrier", q.CarrierName),
				zap.String("scac", q.CarrierSCAC),
				zap.String("tariff", q.TariffName),
				zap.String("reason", q.ErrorMessage),
			)
		}

		out = append(out, mapTaiQuoteToQuickQuote(q, normalizedReq))
	}

	return out, resp.EncryptedQuotes, nil
}

func mapTaiQuoteToQuickQuote(q Quote, req LTLQuickQuoteRequest) models.QuickQuote {
	distance := req.Mileage
	if distance < 0 {
		distance = 0
	}
	div := math.Max(distance, 1)

	load := models.QuoteLoadInfo{
		Distance:             distance,
		FuelSurchargeTotal:   q.BuyFuelSurcharge,
		FuelSurchargePerMile: q.BuyFuelSurcharge / div,
		Customer:             models.CompanyCoreInfo{},
		PickupLocation: models.Address{
			City:  req.OriginCity,
			State: req.OriginState,
			Zip:   req.OriginZip,
		},
		DeliveryLocation: models.Address{
			City:  req.DestinationCity,
			State: req.DestinationState,
			Zip:   req.DestinationZip,
		},
	}

	var totalWeight float64
	var commodity string
	if len(req.LineItems) > 0 {
		commodity = req.LineItems[0].CommodityDescription
		for _, li := range req.LineItems {
			totalWeight += float64(li.TotalWeight)
		}
	}
	load.Commodity = commodity
	load.WeightLbs = totalWeight

	qq := models.QuickQuote{
		QuoteLoadInfo: load,

		Currency:          "USD",
		TotalCost:         q.BuyRateTotal,
		TargetBuyRate:     0,
		LowBuyRate:        0,
		HighBuyRate:       0,
		StartBuyRate:      0,
		FuelRate:          0,
		ConfidenceLevel:   0,
		MinMarkup:         0,
		MaxMarkup:         0,
		MinTargetSellCost: 0,
		MaxTargetSellCost: 0,

		TransitDays:  q.Days,
		ServiceLevel: q.ServiceLevelDisplayName,
		Guaranteed:   q.Guaranteed,
		CarrierName:  q.CarrierName,
		CarrierSCAC:  q.CarrierSCAC,
		IsError:      q.IsError,
		NotServiced:  q.NotServiced,
		ErrorMessage: q.ErrorMessage,

		RateMetadata: map[string]any{
			"sellRateFreightCharges":     q.SellRateFreightCharges,
			"sellFuelSurcharge":          q.SellFuelSurcharge,
			"sellFuelPercentage":         q.SellFuelPercentage,
			"sellAccessorialTotalAmount": q.SellAccessorialTotalAmount,
			"sellRateTotal":              q.SellRateTotal,

			"buyRateFreightCharges":     q.BuyRateFreightCharges,
			"buyFuelSurcharge":          q.BuyFuelSurcharge,
			"buyFuelPercentage":         q.BuyFuelPercentage,
			"buyAccessorialTotalAmount": q.BuyAccessorialTotalAmount,
			"buyRateTotal":              q.BuyRateTotal,

			"accessorialPrices": q.AccessorialPrices,
			"margins":           q.Margins,
		},
	}

	finalCarrierCost := int(math.Round(q.BuyRateTotal))
	finalQuotePrice := int(math.Round(q.SellRateTotal))

	qq.QuoteRequest.AppliedQuote.FinalCarrierCost = finalCarrierCost
	qq.QuoteRequest.AppliedQuote.FinalQuotePrice = finalQuotePrice
	qq.QuoteRequest.AppliedQuote.FinalMargin = finalQuotePrice - finalCarrierCost

	qq.QuoteRequest.AppliedQuote.MarginType = "Amount"
	qq.QuoteRequest.AppliedQuote.CarrierCostType = "Flat"

	return qq
}

type NormalizedLocation struct {
	City    string
	State   string
	Zip     string
	Country string
}

// NormalizeLTLQuickQuoteLocations uses AWS Location to fill out all location fields and normalizes
// them (uppercase city/state, US/empty -> USA).
// Returns a new request with normalized values.
func NormalizeLTLQuickQuoteLocations(ctx context.Context, req LTLQuickQuoteRequest) (LTLQuickQuoteRequest, error) {
	origin, err := normalizeLocation(ctx, req.OriginCity, req.OriginState, req.OriginZip)
	if err != nil {
		log.Error(ctx, "error looking up LTL origin with AWS Location", zap.Error(err))
		return req, fmt.Errorf("failed to look up LTL origin location: %w", err)
	}

	dest, err := normalizeLocation(ctx, req.DestinationCity, req.DestinationState, req.DestinationZip)
	if err != nil {
		log.Error(ctx, "error looking up LTL destination with AWS Location", zap.Error(err))
		return req, fmt.Errorf("failed to look up LTL destination location: %w", err)
	}

	normalized := req
	normalized.OriginCity = origin.City
	normalized.OriginState = origin.State
	normalized.OriginZip = origin.Zip
	normalized.OriginCountry = origin.Country
	normalized.DestinationCity = dest.City
	normalized.DestinationState = dest.State
	normalized.DestinationZip = dest.Zip
	normalized.DestinationCountry = dest.Country

	return normalized, nil
}

func normalizeLocation(ctx context.Context, city, state, zip string) (NormalizedLocation, error) {
	location, err := helpers.AwsLocationLookup(ctx, city, state, zip)
	if err != nil {
		return NormalizedLocation{}, err
	}

	if location == nil || len(location.Results) == 0 {
		return NormalizedLocation{}, fmt.Errorf(
			"no results found for location: city=%q, state=%q, zip=%q",
			city,
			state,
			zip,
		)
	}

	place := location.Results[0].Place

	result := NormalizedLocation{}

	if place.Municipality != nil {
		result.City = strings.ToUpper(*place.Municipality)
	}
	if place.Region != nil {
		state := strings.ToUpper(*place.Region)
		if len(state) > 2 {
			state = helpers.GetStateAbbreviation(ctx, state)
		}

		if state != helpers.UnknownState {
			result.State = state
		}
	}

	if place.PostalCode != nil {
		result.Zip = *place.PostalCode
	}

	if place.Country != nil {
		result.Country = normalizeCountry(*place.Country)
	} else {
		result.Country = "USA"
	}

	return result, nil
}

func normalizeCountry(country string) string {
	country = strings.ToUpper(strings.TrimSpace(country))
	if country == "US" || country == "" {
		return "USA"
	}

	return country
}
