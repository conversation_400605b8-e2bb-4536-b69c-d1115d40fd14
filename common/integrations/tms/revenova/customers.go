package revenova

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/tmsrefresh"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	salesforceQueryPath = "/services/data/v59.0/query"
	customerBatchSize   = 200                    // Salesforce FIELDS function limit
	customerBatchDelay  = 500 * time.Millisecond // Delay between batches to avoid rate limits

	// Redis job types for different states
	customerSyncJob      = "customers"           // Stores timestamp in updatedAt, cursor in cursor field
	customerTimestampJob = "customers-timestamp" // Stores last sync timestamp for incremental sync
)

// GetCustomers retrieves customers from Salesforce with incremental sync support
// - First sync (no timestamp): Full sync with resume capability
// - Subsequent syncs: Incremental sync (only changed records)
// - Failed sync: Resumes from last cursor automatically
func (r *Revenova) GetCustomers(ctx context.Context, opts ...models.TMSOption) ([]models.TMSCustomer, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomersRevenova", otel.IntegrationAttrs(r.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	// Apply options
	options := &models.TMSOptions{}
	options.Apply(opts...)

	// Determine job type based on whether this is a poller job
	jobType := customerSyncJob
	if options.IsPollerJob {
		jobType = redis.CustomerRefreshJob
	}

	var resumeCursor string
	var updatedAtFilter string

	// Step 1: Check for resume cursor (job continuation)
	if options.Cursor != "" {
		resumeCursor = options.Cursor
		log.Info(ctx, "using explicit cursor for customer sync", zap.String("cursor", resumeCursor))
	} else {
		_, savedCursor, err := redis.GetIntegrationState(ctx, r.tms.ID, jobType)
		if err == nil && savedCursor != "" {
			resumeCursor = savedCursor
			log.Info(ctx, "resuming customer sync from saved state", zap.String("cursor", resumeCursor))
		}
	}

	// Step 2: Determine timestamp for incremental sync
	var incrementalSince *time.Time
	syncType := "full"

	if options.UpdatedAtFilter != "" {
		updatedAtFilter = options.UpdatedAtFilter
		if parsed, parseErr := time.Parse(time.RFC3339, updatedAtFilter); parseErr == nil {
			incrementalSince = &parsed
			syncType = "incremental"
		}
	} else {
		// Fallback to Redis state if not a poller job or if poller job wants incremental
		savedUpdatedAt, _, err := redis.GetIntegrationState(ctx, r.tms.ID, jobType)
		if err == nil && savedUpdatedAt != "" {
			if parsed, parseErr := time.Parse(time.RFC3339, savedUpdatedAt); parseErr == nil {
				incrementalSince = &parsed
				syncType = "incremental"
				updatedAtFilter = savedUpdatedAt
			}
		} else if !options.IsPollerJob {
			// Check the dedicated timestamp job only for legacy non-poller calls
			lastSyncTimestamp, _, err := redis.GetIntegrationState(ctx, r.tms.ID, customerTimestampJob)
			if err == nil && lastSyncTimestamp != "" {
				if parsed, parseErr := time.Parse(time.RFC3339, lastSyncTimestamp); parseErr == nil {
					incrementalSince = &parsed
					syncType = "incremental"
					updatedAtFilter = lastSyncTimestamp
				}
			}
		}
	}

	if resumeCursor != "" {
		if incrementalSince != nil {
			log.Info(
				ctx,
				"resuming INCREMENTAL customer sync from previous state",
				zap.String("resumeCursor", resumeCursor),
				zap.Time("incrementalSince", *incrementalSince),
			)
		} else {
			log.Info(
				ctx,
				"resuming FULL customer sync from previous state",
				zap.String("resumeCursor", resumeCursor),
			)
		}

		// Resume with incremental context preserved
		return r.executeCustomerSync(ctx, resumeCursor, incrementalSince, jobType, updatedAtFilter, options)
	}

	// No resume needed - starting fresh sync
	if incrementalSince != nil {
		log.Info(
			ctx,
			"starting incremental customer sync",
			zap.Time("since", *incrementalSince),
			zap.String("sinceStr", updatedAtFilter),
		)
	} else {
		log.Info(ctx, "starting full customer sync")
	}

	// Step 3: Execute sync
	customers, err := r.executeCustomerSync(ctx, "", incrementalSince, jobType, updatedAtFilter, options)
	if err != nil {
		return customers, err
	}

	// Step 4: Sync completed successfully - update timestamp and clear cursor
	now := time.Now()
	nowStr := now.UTC().Format(time.RFC3339)

	// Update sync timestamp state
	if err := redis.SetIntegrationState(ctx, r.tms.ID, customerTimestampJob, nowStr, ""); err != nil {
		log.Error(ctx, "failed to update last sync timestamp", zap.Error(err))
	}

	// Clear resume cursor on successful completion
	if err := redis.ClearIntegrationState(ctx, r.tms.ID, jobType); err != nil {
		log.Warn(ctx, "failed to clear integration state", zap.Error(err))
	}

	log.Info(ctx, "customer sync completed successfully",
		zap.Int("totalCustomers", len(customers)),
		zap.String("syncType", syncType))

	return customers, nil
}

// executeCustomerSync performs the actual sync with cursor-based pagination
// Supports both full sync and incremental sync (if incrementalSince is provided)
func (r *Revenova) executeCustomerSync(
	ctx context.Context,
	resumeFromCursor string,
	incrementalSince *time.Time,
	jobType string,
	updatedAtFilter string,
	options *models.TMSOptions,
) ([]models.TMSCustomer, error) {

	var allCustomers []models.TMSCustomer
	lastID := resumeFromCursor
	batchNumber := 0

	// Determine if this is incremental or full sync
	syncMode := "full"
	if incrementalSince != nil {
		syncMode = "incremental"
	}
	if resumeFromCursor != "" {
		syncMode += "_resume"
	}

	for {
		// Save the state in the options
		if options.OnProgress != nil {
			options.OnProgress(
				updatedAtFilter,
				lastID,
			)
		}

		batchNumber++
		log.Info(
			ctx,
			"fetching customer batch",
			zap.Int("batchNumber", batchNumber),
			zap.Int("fetchedSoFar", len(allCustomers)),
			zap.String("lastID", lastID),
			zap.String("syncMode", syncMode),
		)

		// Fetch batch with optional incremental filter
		customers, err := r.getCustomerBatchWithCursor(ctx, lastID, customerBatchSize, incrementalSince)
		if err != nil {
			log.Error(ctx, "error fetching customer batch", zap.Error(err))

			// Save cursor for resume
			tmsrefresh.SetIntegrationStateWithWarning(
				ctx,
				r.tms.ID,
				jobType,
				updatedAtFilter,
				lastID,
			)

			return allCustomers, fmt.Errorf("failed to get customer batch: %w", err)
		}

		log.Info(
			ctx,
			"received customer batch",
			zap.Int("batchSize", len(customers)),
		)

		if len(customers) == 0 {
			log.Info(ctx, "no more customers - pagination complete!")
			break
		}

		// Convert to TMSCustomer models
		tmsCustomers := make([]models.TMSCustomer, 0, len(customers))
		for _, customer := range customers {
			tmsCustomer := ToCustomerModel(r.tms.ID, customer)
			tmsCustomers = append(tmsCustomers, tmsCustomer)
		}

		log.Info(ctx, "upserting customer batch to database", zap.Int("count", len(tmsCustomers)))

		// Upsert customers to database in batches
		if err = tmsCustomerDB.RefreshTMSCustomers(ctx, &tmsCustomers); err != nil {
			log.Error(ctx, "failed to upsert customers to database", zap.Error(err))

			// Save cursor for resume
			tmsrefresh.SetIntegrationStateWithWarning(
				ctx,
				r.tms.ID,
				jobType,
				updatedAtFilter,
				lastID,
			)

			return allCustomers, fmt.Errorf("failed to upsert customers: %w", err)
		}

		allCustomers = append(allCustomers, tmsCustomers...)

		// Update cursor
		if len(customers) > 0 {
			lastID = customers[len(customers)-1].ID
		}

		log.Info(
			ctx,
			"successfully processed customer batch",
			zap.Int("batchSize", len(customers)),
			zap.Int("totalProcessed", len(allCustomers)),
			zap.String("newLastID", lastID),
		)

		// If partial batch, we're done
		if len(customers) < customerBatchSize {
			log.Info(
				ctx,
				"received partial batch - sync complete!",
				zap.Int("totalFetched", len(allCustomers)),
			)
			break
		}

		// Add delay between batches to avoid hammering Salesforce
		if len(customers) == customerBatchSize {
			log.Debug(
				ctx,
				"adding delay before next customer batch",
				zap.Duration("delay", customerBatchDelay),
			)
			time.Sleep(customerBatchDelay)
		}
	}

	return allCustomers, nil
}

// getCustomerBatchWithCursor fetches a batch of customers using cursor-based pagination
// Supports incremental sync by filtering with incrementalSince timestamp
func (r *Revenova) getCustomerBatchWithCursor(
	ctx context.Context,
	lastID string,
	limit int,
	incrementalSince *time.Time,
) ([]SalesforceAccount, error) {
	queryParams := make(url.Values)

	// Build base SELECT clause with customer type filter
	// (include SystemModstamp for incremental syncs)
	selectClause := `SELECT Id, Name, Phone,
		BillingStreet, BillingCity, BillingState, BillingPostalCode, BillingCountry,
		ShippingStreet, ShippingCity, ShippingState, ShippingPostalCode, ShippingCountry,
		rtms__TMS_Type__c, SystemModstamp
		FROM Account
		WHERE rtms__TMS_Type__c = 'Customer'`

	var soqlQuery string

	if lastID == "" {
		// First query - start with base clause
		soqlQuery = selectClause

		if incrementalSince != nil {
			// Incremental sync: only fetch records modified after timestamp
			soqlQuery += fmt.Sprintf(` AND SystemModstamp > %s`,
				incrementalSince.UTC().Format("2006-01-02T15:04:05Z"))
		}

		soqlQuery += fmt.Sprintf(` ORDER BY Id ASC LIMIT %d`, limit)

	} else {
		// Subsequent queries with cursor
		soqlQuery = fmt.Sprintf(`%s AND Id > '%s'`, selectClause, lastID)

		// Add incremental filter to cursor queries
		if incrementalSince != nil {
			soqlQuery += fmt.Sprintf(` AND SystemModstamp > %s`,
				incrementalSince.UTC().Format("2006-01-02T15:04:05Z"))
		}

		soqlQuery += fmt.Sprintf(` ORDER BY Id ASC LIMIT %d`, limit)
	}

	queryParams.Set("q", soqlQuery)

	log.Info(
		ctx,
		"executing Salesforce SOQL query",
		zap.String("query", soqlQuery),
		zap.String("cursor", lastID),
		zap.Int("limit", limit),
		zap.Bool("incremental", incrementalSince != nil),
	)

	var resp SalesforceQueryResponse
	if err := r.get(ctx, salesforceQueryPath, queryParams, &resp, s3backup.TypeCustomers); err != nil {
		log.Error(ctx, "Salesforce query failed", zap.Error(err))
		return nil, fmt.Errorf("failed to query customers: %w", err)
	}

	log.Info(
		ctx,
		"Salesforce query response",
		zap.Int("totalSize", resp.TotalSize),
		zap.Int("recordCount", len(resp.Records)),
		zap.Bool("done", resp.Done),
	)

	return resp.Records, nil
}

// GetCustomerByID fetches a single customer by ID from Salesforce (like Turvo does)
// This is used to get complete customer details when the load API returns incomplete data
func (r *Revenova) GetCustomerByID(ctx context.Context, customerID string) (SalesforceAccount, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomerByIDRevenova", otel.IntegrationAttrs(r.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "Fetching customer by ID from Salesforce", zap.String("customerID", customerID))

	// Use Salesforce REST API to query Account by ID
	// SELECT FIELDS(ALL) returns all available fields for the account
	// Filter by customer type to ensure we only retrieve customer accounts
	soqlQuery := fmt.Sprintf(
		"SELECT FIELDS(ALL) FROM Account WHERE Id='%s' AND rtms__TMS_Type__c = 'Customer' LIMIT 1",
		customerID,
	)

	queryParams := make(url.Values)
	queryParams.Set("q", soqlQuery)

	var response SalesforceQueryResponse
	if err := r.get(ctx, salesforceQueryPath, queryParams, &response, s3backup.TypeCustomers); err != nil {
		log.Error(ctx, "Failed to query customer from Salesforce", zap.Error(err))
		return SalesforceAccount{}, fmt.Errorf("failed to query customer: %w", err)
	}

	if len(response.Records) == 0 {
		return SalesforceAccount{}, fmt.Errorf("no customer found with ID: %s", customerID)
	}

	log.Info(ctx, "Successfully fetched customer from Salesforce",
		zap.String("customerName", response.Records[0].Name),
		zap.String("customerID", response.Records[0].ID))

	return response.Records[0], nil
}

// ToCustomerModel converts a Salesforce Account to a TMSCustomer model
func ToCustomerModel(tmsID uint, account SalesforceAccount) models.TMSCustomer {
	// Prefer shipping address, fallback to billing address
	addressLine1 := account.ShippingStreet
	city := account.ShippingCity
	state := account.ShippingStateCode
	if state == "" {
		state = account.ShippingState
	}
	zipcode := account.ShippingPostalCode
	country := account.ShippingCountryCode
	if country == "" {
		country = account.ShippingCountry
	}

	// Fallback to billing address if shipping is empty
	if addressLine1 == "" {
		addressLine1 = account.BillingStreet
	}
	if city == "" {
		city = account.BillingCity
	}
	if state == "" {
		state = account.BillingStateCode
		if state == "" {
			state = account.BillingState
		}
	}
	if zipcode == "" {
		zipcode = account.BillingPostalCode
	}
	if country == "" {
		country = account.BillingCountryCode
		if country == "" {
			country = account.BillingCountry
		}
	}

	// Determine email - prefer customer support, then billing contact
	email := account.CustomerSupportEmail
	if email == "" {
		email = account.BillingContactEmail
	}

	companyCoreInfo := models.CompanyCoreInfo{
		ExternalTMSID: account.ID,
		Name:          account.Name,
		AddressLine1:  addressLine1,
		City:          city,
		State:         state,
		Zipcode:       zipcode,
		Country:       country,
		Phone:         account.Phone,
		Email:         email,
	}

	fullAddress := models.ConcatAddress(companyCoreInfo)

	return models.TMSCustomer{
		TMSIntegrationID: tmsID,
		ExternalID:       account.ID,
		CompanyCoreInfo:  companyCoreInfo,
		NameAddress:      models.ConcatNameAddress(account.Name, fullAddress),
	}
}
