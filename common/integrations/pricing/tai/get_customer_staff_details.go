package tai

import (
	"errors"
	"strings"
)

func (c *Client) getCustomerStaffDetails(input SaveLTLQuickQuoteInputRequest) (shipmentDomesticCustomerStaff, error) {
	if input.CustomerOrganizationID == 0 {
		return shipmentDomesticCustomerStaff{}, errors.New("missing customer organization id")
	}

	if input.Request.CustomerStaffID == 0 {
		return shipmentDomesticCustomerStaff{}, errors.New("missing customer staff id")
	}

	contactName := strings.TrimSpace(input.CustomerStaffName)
	if contactName == "" {
		return shipmentDomesticCustomerStaff{}, errors.New("missing customer staff name")
	}

	companyName := strings.TrimSpace(input.CustomerName)
	if companyName == "" {
		companyName = contactName
	}

	return shipmentDomesticCustomerStaff{
		Address:                 map[string]any{},
		BrokerOrganizationID:    input.Request.BrokerID,
		BrokerOrganizationName:  strings.TrimSpace(input.BrokerOrganizationName),
		BrokerOrganizationPhone: "",
		CompanyName:             companyName,
		ContactName:             contactName,
		DimensionsRequired:      false,
		NmfcRequired:            false,
		OrganizationID:          input.CustomerOrganizationID,
		StaffID:                 input.Request.CustomerStaffID,
	}, nil
}
