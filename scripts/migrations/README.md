# Pricing Task Split Migration

This directory contains SQL migration scripts to split the monolithic `PricingLookupTask` into separate `BuyRateLookupTask` and `SellRateCalculationTask`.

## Overview

The migration converts:
- `PricingLookupAction` (step 0) → `BuyRateLookupAction` (step 0) + `SellRateCalculationAction` (step 1)
- All subsequent tasks increment their step number by 1
- Historical invocation data is backfilled to maintain data integrity

## Migration Order

Run these scripts in order during the downtime window:

1. **001_pre_migration_backup.sql** - Creates backup tables
2. **002_split_pricing_task.sql** - Converts task definitions
3. **003_invocation_backfill.sql** - Backfills historical invocation data

## Rollback

If the migration fails or needs to be reverted:

- **004_rollback.sql** - Restores from backup tables

## Cleanup (After migration observation period)

Once the migration is verified and stable:

- **005_cleanup_after_bake.sql** - Drops backup tables

## Pre-Deployment Checklist

- [ ] Deploy code changes with both old and new handlers supported
- [ ] Verify all tests pass
- [ ] Create full database backup (separate from migration backup)
- [ ] Review migration scripts with a second engineer
- [ ] Plan downtime window (estimated 5-10 minutes)
- [ ] Notify stakeholders of maintenance window

## Deployment Steps

### 1. Pre-Deployment

```bash
# Run tests
DISABLE_RATE_LIMIT=true go test ./...

# Deploy code (with both old and new handlers)
# Use your standard deployment process
```

### 2. Downtime Window

```bash
# 1. Pause agent invocations (disable Lambda trigger or set feature flag)

# 2. Connect to database
psql -h <db-host> -U <db-user> -d <db-name>

# 3. Run migrations in order
\i scripts/migrations/001_pre_migration_backup.sql
\i scripts/migrations/002_split_pricing_task.sql
\i scripts/migrations/003_invocation_backfill.sql

# 4. Verify migration (check verification queries output)

# 5. Resume agent invocations
```

### 3. Post-Deployment Verification

```sql
-- Verify no pricingLookup tasks remain
SELECT COUNT(*) FROM quick_quote_agent_tasks WHERE action = 'pricingLookup';
-- Expected: 0

-- Verify buyRateLookup + sellRateCalculation pairs exist
SELECT quick_quote_agent_id, array_agg(action ORDER BY step) 
FROM quick_quote_agent_tasks 
GROUP BY quick_quote_agent_id;
-- Expected: Each agent has [buyRateLookup, sellRateCalculation, ...]

-- Verify step ordering is correct
SELECT quick_quote_agent_id, action, step 
FROM quick_quote_agent_tasks 
ORDER BY quick_quote_agent_id, step;

-- Test an agent invocation manually
-- Watch logs for successful execution
```

### 4. Monitoring

Monitor these metrics for 2 weeks:

- Agent invocation success rate
- Task execution errors
- Email draft generation success rate
- Response time for quote generation

### 5. Cleanup (After 2 weeks)

```bash
# Only run after verifying stability
psql -h <db-host> -U <db-user> -d <db-name>
\i scripts/migrations/005_cleanup_after_bake.sql
```

Then remove deprecated code:
- Remove `PricingLookupAction` handler from task_handlers.go
- Remove `PricingLookupTask` and `PricingLookupTaskInvocation` types (keep for historical queries)
- Remove `PricingLookupTaskResult` from execution context

## Rollback Procedure

If issues are discovered:

```sql
-- Immediately restore from backup
\i scripts/migrations/004_rollback.sql

-- Verify rollback
SELECT COUNT(*) FROM quick_quote_agent_tasks WHERE action = 'pricingLookup';
-- Expected: Original count

-- Resume agent invocations with old code
```

## Risk Assessment

| Risk | Mitigation |
|------|------------|
| Migration fails mid-transaction | All SQL wrapped in transactions; backup tables for rollback |
| Historical data inconsistency | Backfill creates corresponding records; old metadata preserved |
| Handler mismatch during deployment | Deploy code with dual support first, then migrate data |
| Agent invocations during migration | Pause Lambda trigger during migration window |
| Performance degradation | Monitor for 2 weeks before cleanup; easy rollback available |

## Files Modified

- `common/models/agents/quick_quote_agent.go` - New action types and task metadata
- `common/models/agents/quick_quote_invocation.go` - New invocation metadata
- `common/emails/agents/quick_quote/task_orchestrator.go` - Execution context and handlers
- `common/emails/agents/quick_quote/task_handlers.go` - Split handlers
- `common/emails/agents/quick_quote/helpers.go` - New result types
- `common/rds/agents/quick_quote/task/create.go` - Default task creation

## Support

For questions or issues:
- Check logs in CloudWatch for detailed error messages
- Review Braintrust traces for task execution flow
- Contact the backend team lead
