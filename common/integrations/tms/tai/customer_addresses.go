package tai

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/models"
)

type CustomerAddressResp struct {
	CustomerFavoriteAddressID int             `json:"customerFavoriteAddressId"`
	OrganizationID            int             `json:"organizationId"`
	CompanyName               string          `json:"companyName"`
	Address                   CustomerAddress `json:"address"`
	Instructions              string          `json:"instructions"`
	Note                      string          `json:"note"`
	ReferenceNumber           string          `json:"referenceNumber"`
	LocationType              string          `json:"locationType"`
	OpenTime                  string          `json:"openTime"`
	CloseTime                 string          `json:"closeTime"`
	TsaCompliant              bool            `json:"tsaCompliant"`
	Accessorials              []string        `json:"accessorials"`
}

func (t Tai) GetCustomerAddresses(ctx context.Context, customerID int) ([]CustomerAddressResp, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(t.tms), attribute.Int("customer_id", customerID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomerAddressesTai", spanAttrs)
	defer func() { metaSpan.End(err) }()

	endPoint := "/PublicApi/Customer/v2/Addresses"
	queryParams := url.Values{}
	queryParams.Set("customerId", strconv.Itoa(customerID))

	var addresses []CustomerAddressResp
	err = t.get(ctx, endPoint, queryParams, &addresses, s3backup.TypeCustomerAddresses)
	if err != nil {
		return nil, fmt.Errorf("failed to get customer addresses: %w", err)
	}

	return addresses, nil
}

func (t Tai) LoadCustomerAddresses(ctx context.Context, customerID int) ([]models.TMSLocation, error) {
	addresses, err := t.GetCustomerAddresses(ctx, customerID)
	if err != nil {
		return nil, err
	}

	var locations []models.TMSLocation
	for _, address := range addresses {
		locations = append(locations, t.taiAddressToLocation(address))
	}

	return locations, nil
}

func (t Tai) taiAddressToLocation(address CustomerAddressResp) models.TMSLocation {
	location := models.TMSLocation{
		TMSIntegrationID: t.tms.ID,
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: strconv.Itoa(address.OrganizationID),
			Name:          address.CompanyName,
			AddressLine1:  address.Address.StreetAddress,
			AddressLine2:  address.Address.StreetAddressTwo,
			City:          address.Address.City,
			State:         address.Address.State,
			Zipcode:       address.Address.ZipCode,
			Country:       address.Address.Country,
		},
		Instructions:   address.Instructions,
		Note:           address.Note,
		RefNumber:      address.ReferenceNumber,
		LocationType:   address.LocationType,
		OpenTime:       address.OpenTime,
		CloseTime:      address.CloseTime,
		IsTSACompliant: address.TsaCompliant,
		Accessorials:   address.Accessorials,
	}

	location.FullAddress = models.ConcatAddress(location.CompanyCoreInfo)
	location.NameAddress = models.ConcatNameAddress(location.Name, location.FullAddress)

	return location
}
