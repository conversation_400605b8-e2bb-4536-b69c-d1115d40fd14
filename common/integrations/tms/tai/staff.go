package tai

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"go.opentelemetry.io/otel/attribute"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/models"
)

type BrokerStaffResp struct {
	StaffID         int    `json:"staffId"`
	Login           string `json:"login"`
	OrganizationID  int    `json:"organizationId"`
	Email           string `json:"email"`
	ContactName     string `json:"contactName"`
	ReferenceNumber string `json:"referenceNumber"`
	Enabled         bool   `json:"enabled"`
	Phone           string `json:"phone"`
	Mobile          string `json:"mobile"`
	Fax             string `json:"fax"`
	Title           string `json:"title"`
	Address         struct {
		StreetAddress    string `json:"streetAddress"`
		StreetAddressTwo string `json:"streetAddressTwo"`
		City             string `json:"city"`
		State            string `json:"state"`
		ZipCode          string `json:"zipCode"`
		Country          string `json:"country"`
	} `json:"address"`
}

const (
	brokerStaffEndpoint = "/PublicApi/Staff/v2/Brokers"
)

func (t Tai) GetBrokerStaff(ctx context.Context, organizationID int) ([]BrokerStaffResp, error) {
	var err error
	spanAttrs := append(otel.IntegrationAttrs(t.tms), attribute.Int("organization_id", organizationID))
	ctx, metaSpan := otel.StartSpan(ctx, "GetBrokerStaffTai", spanAttrs)
	defer func() { metaSpan.End(err) }()

	queryParams := url.Values{}
	queryParams.Set("organizationId", strconv.Itoa(organizationID))

	var staff []BrokerStaffResp
	err = t.get(ctx, brokerStaffEndpoint, queryParams, &staff, s3backup.TypeBrokerStaff)
	if err != nil {
		return nil, fmt.Errorf("failed to get broker staff: %w", err)
	}

	return staff, nil
}

func (t Tai) TaiBrokerStaffToStaff(staff BrokerStaffResp) models.TMSBrokerStaff {
	return models.TMSBrokerStaff{
		TMSIntegrationID: t.tms.ID,
		ExternalTMSID:    strconv.Itoa(staff.StaffID),
		OrganizationID:   staff.OrganizationID,
		Login:            staff.Login,
		ContactName:      staff.ContactName,
		Email:            staff.Email,
		Phone:            staff.Phone,
		Mobile:           staff.Mobile,
		Fax:              staff.Fax,
		Title:            staff.Title,
		Enabled:          staff.Enabled,
		AddressLine1:     staff.Address.StreetAddress,
		AddressLine2:     staff.Address.StreetAddressTwo,
		City:             staff.Address.City,
		State:            staff.Address.State,
		Zipcode:          staff.Address.ZipCode,
		Country:          staff.Address.Country,
		ReferenceNumber:  staff.ReferenceNumber,
	}
}

func (t Tai) LoadBrokerStaff(ctx context.Context, organizationID int) ([]models.TMSBrokerStaff, error) {
	staff, err := t.GetBrokerStaff(ctx, organizationID)
	if err != nil {
		return nil, err
	}

	out := make([]models.TMSBrokerStaff, 0, len(staff))
	for _, member := range staff {
		out = append(out, t.TaiBrokerStaffToStaff(member))
	}

	return out, nil
}
