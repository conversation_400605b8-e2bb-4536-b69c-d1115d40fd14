-- Main Migration: Split PricingLookupTask into BuyRateLookup + SellRateCalculation
-- This script converts all existing pricingLookup tasks to the new split task structure
-- Must run AFTER pre_migration_backup.sql and BEFORE invocation_backfill.sql

BEGIN;

-- Reset the sequence to match the highest existing ID
SELECT setval(
    'quick_quote_agent_tasks_id_seq',
    (SELECT MAX(id) FROM quick_quote_agent_tasks) + 1
);

-- Step 1: Increment step numbers for all non-pricingLookup tasks by 1
UPDATE quick_quote_agent_tasks
SET step = step + 1
WHERE action != 'pricingLookup';

-- Step 2: Create new sellRateCalculation tasks f   
INSERT INTO quick_quote_agent_tasks (
    quick_quote_agent_id,
    step,
    action,
    max_retries_allowed,
    fail_open,
    metadata,
    name,
    created_by_type,
    created_at,
    updated_at
)
SELECT 
    quick_quote_agent_id,
    1 AS step, 
    'sellRateCalculation' AS action,
    max_retries_allowed,
    false AS fail_open,
    '{"sellRateCalculationTask": {}}' AS metadata,
    'Sell Rate Calculation' AS name,
    created_by_type,
    NOW() AS created_at,
    NOW() AS updated_at
FROM quick_quote_agent_tasks
WHERE action = 'pricingLookup';

-- Step 3: Convert pricingLookup tasks to buyRateLookup
UPDATE quick_quote_agent_tasks
SET 
    action = 'buyRateLookup',
    metadata = jsonb_set(
        COALESCE(metadata, '{}'::jsonb) - 'pricingLookupTask',
        '{buyRateLookupTask}',
        COALESCE(metadata->'pricingLookupTask', '{}'::jsonb)
    ),
    name = COALESCE(NULLIF(name, ''), 'Buy Rate Lookup'),
    updated_at = NOW()
WHERE action = 'pricingLookup';

COMMIT;

-- Verification queries (run after migration)
-- Verify no pricingLookup tasks remain
SELECT 
    'Remaining pricingLookup tasks (should be 0)' AS check_name,
    COUNT(*) AS count
FROM quick_quote_agent_tasks 
WHERE action = 'pricingLookup';

SELECT 
    'Agents with task counts' AS check_name,
    quick_quote_agent_id,
    array_agg(action ORDER BY step) AS actions,
    array_agg(step ORDER BY step) AS steps
FROM quick_quote_agent_tasks 
GROUP BY quick_quote_agent_id;
