package featureflags

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userGroupsDB "github.com/drumkitai/drumkit/common/rds/usergroups"
)

// MergeResult captures the contextual data used when merging feature flags for a user.
type MergeResult struct {
	User         *models.User
	UserGroups   []models.UserGroup
	FeatureFlags models.FeatureFlags
}

// MergeServiceWithUserOverrides fetches the user and their groups, merges feature flags, and mutates the
// provided service to reflect the user-specific overrides. It returns the contextual data for downstream use.
func MergeServiceWithUserOverrides(
	ctx context.Context,
	service *models.Service,
	userID uint,
) (*MergeResult, error) {

	if service == nil {
		return nil, errors.New("service cannot be nil")
	}

	result := &MergeResult{
		FeatureFlags: service.FeatureFlags,
	}

	if userID == 0 {
		// No user provided; return service defaults without mutation side-effects.
		return result, nil
	}

	user, err := rds.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("error getting user %d: %w", userID, err)
	}

	if user.ServiceID != service.ID {
		return nil, fmt.Errorf(
			"user %d belongs to service %d but service %d was provided",
			user.ID,
			user.ServiceID,
			service.ID,
		)
	}

	groups, err := userGroupsDB.GetAllByUserID(ctx, userID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("error getting user groups for user %d: %w", userID, err)
	}

	merged := Merge(service.FeatureFlags, groups, user.FeatureFlagOverrides)
	service.FeatureFlags = merged

	result.User = &user
	result.UserGroups = groups
	result.FeatureFlags = merged

	return result, nil
}
