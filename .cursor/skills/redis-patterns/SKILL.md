---
name: redis-patterns
description: Enforces using Redis wrapper functions from common/redis instead of direct Redis client access. Use when writing or reviewing code that interacts with Red<PERSON>, when the user mentions Redis operations, or when you see direct redis.RDB access patterns.
---

# Redis Access Patterns

## CRITICAL: Always Use Redis Wrapper Functions

**NEVER use direct Redis client access. Always use the wrapper functions from `common/redis`.**

## Forbidden Patterns

```go
// NEVER do this - direct Redis access
redis.RDB.Get(ctx, key)
redis.RDB.Set(ctx, key, value, expiration)
redis.RDB.Del(ctx, key)
redis.RDB.Exists(ctx, key)
redis.RDB.Incr(ctx, key)
redis.RDB.TTL(ctx, key)
redis.RDB.SetNX(ctx, key, value, expiration)
redis.RDB.HGet(ctx, key, field)
redis.RDB.HSet(ctx, key, field, value)
redis.RDB.HExists(ctx, key, field)
redis.RDB.Expire(ctx, key, expiration)
```

## Use Wrapper Functions

Always import and use wrapper functions:

```go
import "github.com/drumkitai/drumkit/common/redis"
```

### Perform Basic Key Operations

```go
// Get values with automatic JSON unmarshaling
// Note: If unmarshaling fails, the corrupted key is automatically deleted
result, found, err := redis.GetKey[string](ctx, key)
result, found, err := redis.GetKey[MyStruct](ctx, key)

// Set values with automatic JSON marshaling
err := redis.SetKey(ctx, key, value, expiration)

// Set with retries (exponential backoff)
err := redis.SetKeyWithRetries(ctx, key, value, expiration)

// Delete keys
err := redis.DeleteKey(ctx, key)

// Check if key exists
exists, err := redis.KeyExists(ctx, key)

// Increment counters (sets expiry on first increment)
count, err := redis.IncrementKey(ctx, key, expiration)

// Get key expiry/TTL
ttl, err := redis.GetKeyExpiry(ctx, key)

// Set if not exists (atomic operation, value must be string)
set, err := redis.SetIfNotExists(ctx, key, "string-value", expiration)
```

### Perform Hash Operations

```go
// Get hash field with automatic JSON unmarshaling
result, found, err := redis.HashGet[MyStruct](ctx, key, field)

// Set hash field with automatic JSON marshaling
err := redis.HashSet(ctx, key, field, data)

// Get hash field as string (no JSON unmarshaling)
// Returns ("", false, nil) if field doesn't exist (no error)
value, found, err := redis.HashGetString(ctx, key, field)

// Set hash field as string (no JSON marshaling)
err := redis.HashSetString(ctx, key, field, value)

// Check if hash field exists
exists, err := redis.HashExists(ctx, key, field)
```

### Manage Connections

```go
// Close Redis connection gracefully
err := redis.Close(ctx)

// Close with timeout
err := redis.CloseWithTimeout(ctx, timeout)
```

### Handle Errors

Wrapper functions provide consistent error handling, logging, and Sentry reporting:
- Return `redis.NilEntry` when key doesn't exist
- `IncrementKey`, `GetKeyExpiry`, and `SetIfNotExists` return errors if RDB is nil
- `HashGetString` returns `("", false, nil)` when field doesn't exist (no error)

## Extend Wrapper Functions if Needed

If wrapper functions don't support your use case, extend them in `common/redis` rather than using direct access.
