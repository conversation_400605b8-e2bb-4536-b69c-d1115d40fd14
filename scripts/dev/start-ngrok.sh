#!/bin/bash

# Script to start ngrok and update fn/api/.env with the webhook URL

set -e

# Set to your root Beacon/Drumkit directory
DRUMKIT_ROOT="/Users/<USER>/drumkit"
ENV_FILE="${DRUMKIT_ROOT}/fn/api/.env"
NGROK_PORT=5006 # Default port for Outlook ingestion lambda in dev

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting ngrok on port ${NGROK_PORT}...${NC}"

# Check if ngrok is already running (suppress errors)
NGROK_PID=""
if command -v pgrep > /dev/null 2>&1; then
    NGROK_PID=$(pgrep -f "ngrok http ${NGROK_PORT}" 2>/dev/null | head -1)
fi

if [ ! -z "$NGROK_PID" ]; then
    echo -e "${YELLOW}ngrok is already running on port ${NGROK_PORT}${NC}"
    echo -e "${YELLOW}Using existing ngrok instance (PID: ${NGROK_PID})${NC}"
else
    # Start ngrok in the background
    ngrok http ${NGROK_PORT} > /tmp/ngrok.log 2>&1 &
    NGROK_PID=$!
    echo -e "${GREEN}Started ngrok (PID: ${NGROK_PID})${NC}"
    
    # Wait for ngrok to start
    echo -e "${YELLOW}Waiting for ngrok to start...${NC}"
    sleep 5
fi

# Try to get the ngrok URL from the API
NGROK_URL=""
MAX_ATTEMPTS=20
for i in $(seq 1 $MAX_ATTEMPTS); do
    # Wait a bit before first attempt if we just started ngrok
    if [ $i -eq 1 ] && [ -z "$NGROK_PID" ]; then
        sleep 2
    fi
    
    # Try to get HTTPS URL first, then HTTP
    API_RESPONSE=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null)
    if [ ! -z "$API_RESPONSE" ]; then
        NGROK_URL=$(echo "$API_RESPONSE" | grep -o '"public_url":"https://[^"]*"' | head -1 | cut -d'"' -f4)
        if [ -z "$NGROK_URL" ]; then
            NGROK_URL=$(echo "$API_RESPONSE" | grep -o '"public_url":"http://[^"]*"' | head -1 | cut -d'"' -f4)
        fi
    fi
    
    if [ ! -z "$NGROK_URL" ]; then
        break
    fi
    
    if [ $i -lt $MAX_ATTEMPTS ]; then
        sleep 1
    fi
done

if [ -z "$NGROK_URL" ]; then
    echo -e "${RED}Error: Could not detect ngrok URL after ${MAX_ATTEMPTS} attempts.${NC}"
    echo -e "${YELLOW}Please check: http://localhost:4040${NC}"
    echo -e "${YELLOW}You can manually update ${ENV_FILE}${NC}"
    echo ""
    echo -e "${YELLOW}To get the URL manually:${NC}"
    echo "  1. Visit http://localhost:4040"
    echo "  2. Copy the 'Forwarding' URL"
    echo "  3. Update ${ENV_FILE} with: MICROSOFT_WEBHOOK_URL={url}/inboxWebhook"
    
    # Try to get PID from ps if pgrep failed
    if [ -z "$NGROK_PID" ]; then
        NGROK_PID=$(ps aux | grep "[n]grok http ${NGROK_PORT}" | awk '{print $2}' | head -1)
        if [ ! -z "$NGROK_PID" ]; then
            echo ""
            echo -e "${GREEN}Found ngrok process (PID: ${NGROK_PID})${NC}"
        fi
    fi
    
    exit 1
fi

echo -e "${GREEN}Found ngrok URL: ${NGROK_URL}${NC}"

# Update the .env file
if [ ! -f "$ENV_FILE" ]; then
    echo -e "${RED}Error: .env file not found at ${ENV_FILE}${NC}"
    exit 1
fi

# Create a backup
cp "$ENV_FILE" "${ENV_FILE}.backup"
echo -e "${GREEN}Created backup: ${ENV_FILE}.backup${NC}"

# Update MICROSOFT_WEBHOOK_URL with /inboxWebhook path
NEW_WEBHOOK_URL="${NGROK_URL}/inboxWebhook"

if grep -q "^MICROSOFT_WEBHOOK_URL=" "$ENV_FILE"; then
    # Use sed to update the line (works on macOS)
    sed -i '' "s|^MICROSOFT_WEBHOOK_URL=.*|MICROSOFT_WEBHOOK_URL=${NEW_WEBHOOK_URL}|" "$ENV_FILE"
    echo -e "${GREEN}✓ Updated MICROSOFT_WEBHOOK_URL in .env${NC}"
    echo -e "${GREEN}  New URL: ${NEW_WEBHOOK_URL}${NC}"
else
    echo -e "${YELLOW}Warning: MICROSOFT_WEBHOOK_URL not found in .env${NC}"
    echo -e "${YELLOW}Adding it to the end of the file...${NC}"
    echo "MICROSOFT_WEBHOOK_URL=${NEW_WEBHOOK_URL}" >> "$ENV_FILE"
    echo -e "${GREEN}✓ Added MICROSOFT_WEBHOOK_URL to .env${NC}"
fi

echo ""
echo -e "${GREEN}✓ ngrok is running on port ${NGROK_PORT}${NC}"
echo -e "${GREEN}✓ Webhook URL updated: ${NEW_WEBHOOK_URL}${NC}"
echo ""
echo "To stop ngrok, run: kill ${NGROK_PID}"
echo "Or visit http://localhost:4040 to see the ngrok dashboard"
