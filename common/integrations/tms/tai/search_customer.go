package tai

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
)

const searchCustomersEndpoint = "/api/customer/typeahead"

func (t Tai) SearchCustomers(ctx context.Context, query string) ([]CustomerSearchResult, error) {
	client, err := t.GetAuthenticatedWebClient(ctx)
	if err != nil {
		return nil, err
	}

	return t.searchCustomersWithClient(ctx, client, query)
}

func (t Tai) searchCustomersWithClient(
	ctx context.Context,
	client *http.Client,
	query string,
) ([]CustomerSearchResult, error) {
	searchURL := url.URL{
		Scheme:   "https",
		Host:     t.host,
		Path:     searchCustomersEndpoint,
		RawQuery: url.Values{"query": []string{query}}.Encode(),
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, searchURL.String(), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create search request: %w", err)
	}

	req.Header.Set("Accept", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("search request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("search returned status %d: %s", resp.StatusCode, string(body))
	}

	var customers []CustomerSearchResult
	if err := json.Unmarshal(body, &customers); err != nil {
		return nil, fmt.Errorf("failed to parse search results: %w", err)
	}

	return customers, nil
}
