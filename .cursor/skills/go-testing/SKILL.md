---
name: go-testing
description: Write comprehensive Go tests following drumkit backend patterns. Use when writing unit tests, integration tests, API validation tests, Redis tests, database tests, or when the user asks about testing, test coverage, or test patterns.
---

# Go Testing for Drumkit Backend

## Core Principles

1. Place test files next to code: `handler.go` → `handler_test.go`
2. Use table-driven tests for multiple scenarios
3. Test both success and error paths
4. Use `testify/assert` and `testify/require` appropriately
5. Mock external dependencies (Redis, external APIs)

## Table-Driven Test Template

```go
func TestFunctionName(t *testing.T) {
    tests := []struct {
        name          string
        input         InputType
        expected      ExpectedType
        expectedError string
    }{
        {
            name:     "successful case",
            input:     validInput,
            expected:  expectedOutput,
        },
        {
            name:          "error case",
            input:         invalidInput,
            expectedError: "expected error message",
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result, err := FunctionName(tt.input)
            
            if tt.expectedError != "" {
                require.Error(t, err)
                assert.Contains(t, err.Error(), tt.expectedError)
                return
            }
            
            require.NoError(t, err)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

## Mock Redis with redismock

**CRITICAL**: When mocking `GetKey[T]()`, use JSON-encoded values:

```go
import (
    "github.com/go-redis/redismock/v9"
    "github.com/drumkitai/drumkit/common/redis"
)

func TestWithRedis(t *testing.T) {
    ctx := context.Background()
    rdb, mock := redismock.NewClientMock()
    redis.SetRDB(rdb)
    defer redis.SetRDB(nil)

    // ✅ Correct - JSON-encoded
    mock.ExpectGet("key").SetVal(`"myvalue"`)
    mock.ExpectGet("struct-key").SetVal(`{"field":"value"}`)
    
    // ❌ Wrong - raw string causes unmarshal error
    // mock.ExpectGet("key").SetVal("myvalue")
    
    result, found, err := redis.GetKey[string](ctx, "key")
    require.NoError(t, err)
    assert.True(t, found)
    assert.Equal(t, "myvalue", result)
}
```

### Disable Redis for Non-Redis Tests

```go
func TestMain(m *testing.M) {
    os.Setenv("DISABLE_RATE_LIMIT", "true")
    exitCode := m.Run()
    os.Unsetenv("DISABLE_RATE_LIMIT")
    os.Exit(exitCode)
}
```

## Test Live Database with TEST_LIVE_DB

**Location Restriction**: Live database tests must be in `common/rds/` or `common/models/` only (CI workflow dependency).

```go
import "github.com/drumkitai/drumkit/common/rds"

func TestDatabaseOperation(t *testing.T) {
    if os.Getenv("TEST_LIVE_DB") != "true" {
        t.Skip("Skipping live DB test")
    }
    
    ctx := rds.SetupTestDB(t)
    service := rds.CreateTestService(ctx, t)
    user := rds.CreateTestUser(ctx, t, service.ID)
    
    // test implementation
}
```

## Test API Validation

Use `apitesting.NewValidationTest()` for route validation:

```go
import apitesting "github.com/drumkitai/drumkit/fn/api/testing"

func TestRouteValidation(t *testing.T) {
    validator := apitesting.NewValidationTest(t)

    t.Run("valid query params", func(t *testing.T) {
        query := MyQuery{RequiredField: "value"}
        if err := validator.TestQuery(query); err != nil {
            t.Errorf("Expected no error, got: %v", err)
        }
    })

    t.Run("missing required field", func(t *testing.T) {
        query := MyQuery{} // RequiredField omitted
        err := validator.TestQuery(query)
        require.Error(t, err)
        
        var validationErr *apitesting.ValidationError
        require.True(t, errors.As(err, &validationErr))
        assert.True(t, validationErr.Contains("requiredField is a required field"))
    })
}
```

### Choose Appropriate Test Method

```go
validator.TestQuery(query)              // Query-only routes (GET)
validator.TestBody(body)                // Body-only routes (POST)
validator.TestBodyAndQuery(query, body) // Routes with both
```

Always test: valid data, missing required fields, embedded structs, field types, time formatting.

## Best Practices

### Use Descriptive Test Names

```go
// ✅ Good
func TestGetUser_ReturnsNotFound_WhenUserDoesNotExist(t *testing.T)
func TestCreateOrder_ValidatesRequiredFields(t *testing.T)

// ❌ Bad
func TestGetUser(t *testing.T)
```

### Use require vs assert Appropriately

```go
require.NoError(t, err)           // Stop test if setup fails
require.NotNil(t, result)         // Stop test if precondition fails
assert.Equal(t, expected, actual) // Continue test to see all failures
```

### Always Test Error Paths

```go
t.Run("returns error on invalid input", func(t *testing.T) {
    _, err := Function(invalidInput)
    require.Error(t, err)
    assert.Contains(t, err.Error(), "expected error message")
})
```

## Run Tests

```bash
DISABLE_RATE_LIMIT=true go test ./...           # All unit tests
go test -run TestFunctionName ./path/to/package # Single test
TEST_LIVE_DB=true go test -p=1 ./...            # Live database tests
go test -v ./...                                # Verbose output
```