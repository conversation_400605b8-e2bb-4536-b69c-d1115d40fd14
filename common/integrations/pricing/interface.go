package pricing

import (
	"context"
	"errors"
	"fmt"

	"github.com/drumkitai/drumkit/common/integrations/pricing/tai"
	"github.com/drumkitai/drumkit/common/integrations/pricing/threeg"
	"github.com/drumkitai/drumkit/common/models"
)

type LTLQuoteLineItem = tai.QuickQuoteLineItem

// NOTE: LTLQuoteRequest is currently an alias to the Tai request type.
// We can replace this alias later and adapt it when we're adding more integrations to this interface.
type LTLQuoteRequest = tai.LTLQuickQuoteRequest
type SaveLTLQuickQuoteInputRequest = tai.SaveLTLQuickQuoteInputRequest
type SaveLTLQuickQuoteResult = tai.SaveLTLQuickQuoteResult

// threegWrapper wraps a threeg.Client to implement LTLInterface with proper type conversion
type threegWrapper struct {
	*threeg.Client
}

// GetLTLQuickQuotes converts the request type and delegates to the wrapped client
func (w *threegWrapper) GetLTLQuickQuotes(ctx context.Context, req LTLQuoteRequest) ([]models.QuickQuote, string, error) {
	// Convert tai.LTLQuickQuoteRequest to threeg.LTLQuoteRequest
	threegReq := threeg.LTLQuoteRequest{}
	return w.Client.GetLTLQuickQuotes(ctx, threegReq)
}

func (w *threegWrapper) GetStaticServiceLevels() []string {
	return w.Client.GetStaticServiceLevels()
}

func (w *threegWrapper) SaveLTLQuickQuote(ctx context.Context, req SaveLTLQuickQuoteInputRequest) (SaveLTLQuickQuoteResult, error) {
	threegReq := threeg.SaveLTLQuickQuoteInputRequest{}

	_, err := w.Client.SaveLTLQuickQuote(ctx, threegReq)

	return SaveLTLQuickQuoteResult{}, err
}

type LTLInterface interface {
	SearchCustomers(ctx context.Context, query string) ([]models.TMSCustomer, error)
	SearchCustomerStaff(
		ctx context.Context,
		customerOrganizationID int,
		customerOrganizationName string,
		enabled bool,
	) ([]models.TMSCustomer, error)

	GetCustomerAccessorials(
		ctx context.Context,
		customerOrgID,
		brokerID,
		shipmentType int,
	) ([]models.Accessorial, error)
	GetStaticCustomerAccessorials() []models.Accessorial

	GetMileageByZipCodes(
		ctx context.Context,
		customerOrganizationID,
		pcmilerRoutingType int,
		origin,
		destination models.Address,
	) (float64, error)

	GetLTLQuickQuotes(
		ctx context.Context,
		req LTLQuoteRequest,
	) ([]models.QuickQuote, string, error)

	GetStaticServiceLevels() []string

	SaveLTLQuickQuote(ctx context.Context, req SaveLTLQuickQuoteInputRequest) (SaveLTLQuickQuoteResult, error)
}

func New(ctx context.Context, pricing models.Integration, opts ...models.PricingOption) (LTLInterface, error) {
	options := &models.PricingOptions{
		UserEmail: pricing.Username,
	}

	for _, opt := range opts {
		opt(options)
	}

	switch pricing.Name {
	case models.TaiPricing:
		if options.UserEmail == "" {
			//nolint:lll
			return nil, errors.New("user email is required for Tai pricing (provide WithUserEmail) or set pricing.Username")
		}

		return tai.New(ctx, pricing, options.UserEmail)

	case models.ThreeGPricing:
		client, err := threeg.New(ctx, pricing)
		if err != nil {
			return nil, err
		}
		return &threegWrapper{Client: client}, nil
	default:
		return nil, fmt.Errorf("unknown pricing integration %s", pricing.Name)
	}
}
