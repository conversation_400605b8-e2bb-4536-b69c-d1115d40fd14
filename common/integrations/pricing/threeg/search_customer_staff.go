package threeg

import (
	"context"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
)

// SearchCustomerStaff fetches staff for a given customer organization.
// enabled=true maps to enabled=1; enabled=false maps to enabled=0.
func (c *Client) SearchCustomerStaff(
	ctx context.Context,
	customerOrganizationID int,
	customerOrganizationName string,
	enabled bool,
) ([]models.TMSCustomer, error) {
	return nil, helpers.NotImplemented(models.ThreeG, "SearchCustomerStaff")
}
