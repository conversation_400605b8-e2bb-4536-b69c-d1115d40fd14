package featureflags

import (
	"slices"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/drumkitai/drumkit/common/models"
)

func boolPtr(value bool) *bool {
	return &value
}

func TestMergePriorityOrder(t *testing.T) {
	serviceFlags := models.FeatureFlags{
		IsQuickQuoteEnabled:            false,
		IsLTLQuickQuoteEnabled:         false,
		IsLoadBuildingEnabled:          true,
		IsCarrierNetworkQuotingEnabled: false,
	}

	groupTrue := true
	groupFalse := false

	userGroups := []models.UserGroup{
		{
			FeatureFlagOverrides: models.FeatureFlagOverrides{
				"isQuickQuoteEnabled":    &groupTrue,
				"isLTLQuickQuoteEnabled": &groupTrue,
			},
		},
		{
			FeatureFlagOverrides: models.FeatureFlagOverrides{
				"isQuickQuoteEnabled": &groupFalse,
			},
		},
	}

	userOverrides := models.FeatureFlagOverrides{
		"isLTLQuickQuoteEnabled":         boolPtr(false),
		"isCarrierNetworkQuotingEnabled": boolPtr(true),
	}

	merged := Merge(serviceFlags, userGroups, userOverrides)

	assert.True(
		t,
		merged.IsQuickQuoteEnabled,
		"expected IsQuickQuoteEnabled to be true from user group override",
	)

	assert.False(
		t,
		merged.IsLTLQuickQuoteEnabled,
		"expected IsLTLQuickQuoteEnabled to remain false due to user override",
	)

	assert.True(
		t,
		merged.IsCarrierNetworkQuotingEnabled,
		"expected IsCarrierNetworkQuotingEnabled to be true from user override",
	)

	assert.Equal(
		t,
		serviceFlags.IsLoadBuildingEnabled,
		merged.IsLoadBuildingEnabled,
		"expected unrelated service flags to remain unchanged",
	)
}

func TestMergeAppliesOverridesCaseInsensitive(t *testing.T) {
	serviceFlags := models.FeatureFlags{}
	userGroups := []models.UserGroup{
		{
			FeatureFlagOverrides: models.FeatureFlagOverrides{
				"IsloadBuildingEnabled": boolPtr(true),
			},
		},
	}
	userOverrides := models.FeatureFlagOverrides{
		"IsLtlQuickQuoteEnabled": boolPtr(true),
	}

	merged := Merge(serviceFlags, userGroups, userOverrides)

	assert.True(
		t,
		merged.IsLoadBuildingEnabled,
		"expected IsLoadBuildingEnabled to be true from case-insensitive group override",
	)

	assert.True(
		t,
		merged.IsLTLQuickQuoteEnabled,
		"expected IsLTLQuickQuoteEnabled to be true from case-insensitive user override",
	)
}

func TestGetValidFeatureFlagsIncludesKnownFields(t *testing.T) {
	flags := GetValidFeatureFlags()

	mustContain := []string{"isQuickQuoteEnabled", "isLTLQuickQuoteEnabled", "isLoadViewEnabled"}

	for _, key := range mustContain {
		assert.Truef(t, slices.Contains(flags, key), "expected GetValidFeatureFlags to include %s", key)
	}
}
