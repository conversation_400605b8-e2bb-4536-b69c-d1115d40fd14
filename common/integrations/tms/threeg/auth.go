package threeg

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
)

const (
	requiredCookie = "JSESSIONID"
)

// hasValidSession checks if we have a valid session cookie in the cookie jar.
// Returns false if session is invalid or expired, and clears expired cookies from Redis.
func (t *ThreeG) hasValidSession() bool {
	t.mu.RLock()
	sessionValid := t.sessionValid
	lastLoginTime := t.lastLoginTime
	t.mu.RUnlock()

	// If we haven't logged in yet or session was invalidated, return false
	if !sessionValid || lastLoginTime.IsZero() {
		return false
	}

	// Defensive check: ensure cookie jar exists
	if t.client == nil || t.client.Jar == nil {
		return false
	}

	// Check if session cookie exists in jar
	parsedURL, err := url.Parse(t.webBaseURL)
	if err != nil {
		return false
	}

	cookies := t.client.Jar.Cookies(parsedURL)
	for _, cookie := range cookies {
		if cookie.Name == requiredCookie {
			// Cookie jar already filters expired cookies internally.
			// If Cookies() returns a cookie, it's valid (not expired).
			return true
		}
	}

	return false
}

// invalidateSession marks the session as invalid and clears Redis cache (e.g., after a 401/403 response)
func (t *ThreeG) invalidateSession(ctx context.Context) {
	t.mu.Lock()
	t.sessionValid = false
	t.mu.Unlock()

	// Clear cached cookies from Redis
	cacheKey := buildCookieCacheKey(t.integration)
	if err := deleteCookiesFromRedis(ctx, cacheKey); err != nil {
		log.Warn(ctx, "failed to invalidate session cookies in redis", zap.Error(err))
	}
}

// login performs authentication with 3G TMS and returns the session cookie.
// This function is idempotent - it checks for an existing valid session before authenticating.
func (t *ThreeG) login(ctx context.Context) error {
	// Check if we already have a valid session
	if t.hasValidSession() {
		log.Debug(ctx, "reusing existing 3G TMS session")
		return nil
	}

	// Use the web base URL from the ThreeG struct (handles custom hostnames)
	endpoint := fmt.Sprintf("%s/web/resources/j_spring_security_check", t.webBaseURL)

	// Decrypt the password
	decryptedPassword, err := crypto.DecryptAESGCM(ctx, string(t.integration.EncryptedPassword), nil)
	if err != nil {
		return fmt.Errorf("error decrypting password: %w", err)
	}

	// Create form data for login
	formData := url.Values{}
	formData.Set("j_username", t.integration.Username)
	formData.Set("j_password", decryptedPassword)

	// Create the request
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, endpoint, strings.NewReader(formData.Encode()))
	if err != nil {
		return fmt.Errorf("error creating login request: %w", err)
	}

	// Set headers to match the successful curl request
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	//nolint:lll
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("DNT", "1")
	req.Header.Set("Origin", t.webBaseURL)
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Referer", fmt.Sprintf("%s/web/login", t.webBaseURL))
	req.Header.Set("Sec-Ch-Ua", `"Chromium";v="137", "Not/A)Brand";v="24"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"macOS"`)
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	//nolint:lll
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36")

	// Make the request
	resp, err := t.client.Do(req)
	if err != nil {
		return fmt.Errorf("error making login request: %w", err)
	}
	defer resp.Body.Close()

	// Check for successful login
	if resp.StatusCode != http.StatusOK {
		// Read response body for error details
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("login failed with status: %s (could not read response body)", resp.Status)
		}
		return fmt.Errorf("login failed with status: %s - response: %s", resp.Status, string(body))
	}

	// Get cookies from jar - it processes all Set-Cookie headers from all responses
	parsedURL, err := url.Parse(endpoint)
	if err != nil {
		return fmt.Errorf("error parsing URL: %w", err)
	}

	jarCookies := t.client.Jar.Cookies(parsedURL)
	if len(jarCookies) == 0 {
		return errors.New("no session cookie received after login")
	}

	// Verify JSESSIONID exists in jar
	found := false
	for _, cookie := range jarCookies {
		if cookie.Name == requiredCookie {
			found = true
			break
		}
	}

	if !found {
		t.invalidateSession(ctx)
		return fmt.Errorf("missing required cookie after login: %s", requiredCookie)
	}

	// Mark session as valid and update login time
	t.mu.Lock()
	t.sessionValid = true
	t.lastLoginTime = time.Now()
	t.mu.Unlock()

	// Save cookies to Redis for reuse across Lambda invocations
	// jar.Cookies() doesn't populate domain/path/secure/httpOnly, so we set them based on the URL
	serialized := make([]SerializableCookie, 0, len(jarCookies))
	for _, cookie := range jarCookies {
		// Encrypt the cookie value
		encryptedValue, err := crypto.EncryptAESGCM(ctx, cookie.Value, nil)
		if err != nil {
			log.Warn(ctx, "failed to encrypt cookie for Redis", zap.String("name", cookie.Name), zap.Error(err))
			continue
		}

		// Set cookie attributes based on URL (jar.Cookies() doesn't populate these)
		domain := parsedURL.Hostname()
		path := "/"
		secure := parsedURL.Scheme == "https"
		httpOnly := true // Session cookies should be httpOnly

		serialized = append(serialized, SerializableCookie{
			Name:     cookie.Name,
			Domain:   domain,
			Path:     path,
			Secure:   secure,
			HTTPOnly: httpOnly,
			Expires:  cookie.Expires,
			ValueEnc: []byte(encryptedValue),
		})
	}

	if len(serialized) > 0 {
		ttl := computeTTL(jarCookies)
		cacheKey := buildCookieCacheKey(t.integration)
		if err := saveCookiesToRedis(ctx, cacheKey, serialized, ttl); err != nil {
			log.Warn(ctx, "failed to save cookies to redis", zap.Error(err))
		}
	} else {
		log.Warn(ctx, "no cookies serialized for Redis after login (encryption failures)")
	}

	log.Debug(ctx, "successfully authenticated with 3G TMS")
	return nil
}

// ensureAuthenticated ensures we have a valid session, logging in if necessary.
// This is the preferred method to call before making API requests.
func (t *ThreeG) ensureAuthenticated(ctx context.Context) error {
	if t.hasValidSession() {
		return nil
	}

	return t.login(ctx)
}

// doWithAuthRetry executes an HTTP request with automatic retry on 401/403 responses.
// If a 401/403 is received, it invalidates the session, re-authenticates, and retries once.
// This should be used to wrap all web API calls that require session authentication.
// Note: For requests with bodies, ensure req.GetBody is set (http.NewRequestWithContext sets this automatically).
func (t *ThreeG) doWithAuthRetry(ctx context.Context, req *http.Request) (*http.Response, error) {
	// Ensure we're authenticated before making the request
	if err := t.ensureAuthenticated(ctx); err != nil {
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	// Make the request
	resp, err := t.client.Do(req)
	if err != nil {
		return resp, err
	}

	// Check for authentication failures
	if resp.StatusCode == http.StatusUnauthorized || resp.StatusCode == http.StatusForbidden {
		log.Warn(
			ctx,
			"received 401/403 response, invalidating session and retrying",
			zap.Int("status_code", resp.StatusCode),
			zap.String("url", req.URL.String()),
			zap.String("method", req.Method),
		)

		// Close the failed response body
		resp.Body.Close()

		// Invalidate session and re-authenticate
		t.invalidateSession(ctx)
		if err := t.login(ctx); err != nil {
			return nil, fmt.Errorf("re-authentication failed after 401/403: %w", err)
		}

		// Retry the request once
		// http.NewRequestWithContext automatically sets GetBody for strings.Reader, bytes.Reader, etc.
		var retryReq *http.Request
		switch {
		case req.GetBody != nil:
			// Request has GetBody function, use it to recreate body
			body, getBodyErr := req.GetBody()
			if getBodyErr != nil {
				return nil, fmt.Errorf("failed to recreate request body for retry: %w", getBodyErr)
			}
			retryReq = req.Clone(ctx)
			retryReq.Body = body
		case req.Body == nil:
			// No body, safe to clone directly
			retryReq = req.Clone(ctx)
		default:
			// Body exists but no GetBody - this shouldn't happen with http.NewRequestWithContext
			// but if it does, we can't safely retry
			return nil, errors.New("cannot retry request: body consumed and no GetBody function available")
		}

		retryResp, retryErr := t.client.Do(retryReq)
		if retryErr != nil {
			return retryResp, fmt.Errorf("retry request failed: %w", retryErr)
		}

		// Check if retry also failed with auth error (shouldn't happen, but handle gracefully)
		if retryResp.StatusCode == http.StatusUnauthorized || retryResp.StatusCode == http.StatusForbidden {
			retryResp.Body.Close()
			return nil, fmt.Errorf(
				"retry request also returned %d after re-authentication - credentials may be invalid",
				retryResp.StatusCode,
			)
		}

		log.Info(
			ctx,
			"successfully retried request after re-authentication",
			zap.Int("status_code", retryResp.StatusCode),
			zap.String("url", req.URL.String()),
		)

		return retryResp, nil
	}

	return resp, nil
}
