package emails

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/prompts"
	"github.com/drumkitai/drumkit/common/rds"
	genEmailDB "github.com/drumkitai/drumkit/common/rds/generatedemails"
)

// getLabelsForQuoting is a helper function that handles the labelling flow for the quoting category using the service's
// feature flags, as those emails present a unique LLM challenge since quoting labels are difficult to distinguish.
func getLabelsForQuoting(
	ctx context.Context,
	openaiService openai.Service,
	email models.IngestedEmail,
	previousResponseID string,
	service *models.Service,
) ([]string, string, error) {

	var err error
	var fetched models.Service

	svc := service
	if svc == nil {
		fetched, err = rds.GetServiceByID(ctx, email.ServiceID)
		if err != nil {
			errMsg := "failed to get service"
			if errors.Is(err, gorm.ErrRecordNotFound) {
				errMsg = "service not found for ingested email"
			}

			log.Error(ctx, errMsg, zap.Error(err))
			return nil, "", fmt.Errorf("failed to get service: %w", err)
		}

		svc = &fetched
	}

	log.Info(
		ctx,
		"resolving quoting labels with merged feature flags",
		zap.Uint("serviceID", svc.ID),
		zap.Uint("userID", email.UserID),
		zap.Any("featureFlags", svc.FeatureFlags),
	)

	isLTLQuickQuoteEnabled := svc.IsLTLQuickQuoteEnabled

	var labels []string
	labels, err = determineQuoteRequestTypeAndLabels(
		ctx,
		openaiService,
		getEmailFromIngestedEmail(email),
		previousResponseID,
		svc.IsQuickQuoteEnabled,
		isLTLQuickQuoteEnabled,
	)

	if err != nil {
		log.Error(ctx, "llm error determining quote request email labels", zap.Error(err))
		switch {
		case !svc.IsQuickQuoteEnabled && isLTLQuickQuoteEnabled:
			// NOTE: Future-proofing to tag as LTL when only LTL quote support is available.
			labels = append(labels, string(LTLQuoteRequestLabel))

		case svc.IsQuickQuoteEnabled || isLTLQuickQuoteEnabled:
			// One quote feature flag is available so we fall back to TL quote request.
			labels = append(labels, string(QuoteRequestLabel))
		}
	}

	// Carrier quote labeling
	if svc.IsCarrierNetworkQuotingEnabled {
		carrierEmail, err := genEmailDB.GetEmailAndQuoteRequestByThreadID(ctx, email.ThreadID)
		if err != nil {
			logMsg := "error getting associated quote request thread from DB"
			logFunc := log.Error

			if errors.Is(err, gorm.ErrRecordNotFound) {
				logMsg = "email not associated with carrier quote flow, skip carrier_quote label"
				logFunc = log.Info
			}

			logFunc(ctx, logMsg, zap.Error(err))
			return labels, "", nil
		}

		if carrierEmail != nil && len(carrierEmail.QuoteRequests) > 0 {
			labels = append(labels, string(CarrierQuoteResponseLabel))
		}
	}

	return labels, "", nil
}

type determineQuoteRequestLabelsOutput struct {
	IsBatch bool `json:"is_batch"`
	IsLTL   bool `json:"is_ltl"`
}

// determineQuoteRequestTypeAndLabels classifies an email as a single or batch quote request and returns its associated
// labels (e.g. LTL, Quick Quote) based on the email content and feature flag settings.
func determineQuoteRequestTypeAndLabels(
	ctx context.Context,
	openaiService openai.Service,
	email models.Email,
	previousResponseID string,
	isQuickQuoteEnabled,
	isLTLQuickQuoteEnabled bool,
) ([]string, error) {

	developerPrompt := prompts.BuildDetermineQuoteRequestLabelsSystemPrompt(
		isQuickQuoteEnabled,
		isLTLQuickQuoteEnabled,
	)

	response, err := openaiService.GetResponse(
		ctx,
		email,
		models.Attachment{},
		braintrustsdk.CreateProjectDetails(braintrustsdk.QREmailLabeling, false),
		openai.ResponseOptions{
			PreviousResponseID: previousResponseID,
			DeveloperPrompt:    developerPrompt,
			Schema:             extractor.GenerateSchema[determineQuoteRequestLabelsOutput](),
		},
	)
	if err != nil {
		return nil, fmt.Errorf("error getting response from LLM: %w", err)
	}

	result, err := extractor.StructExtractor[determineQuoteRequestLabelsOutput](response.Content)
	if err != nil {
		return nil, fmt.Errorf("failed to parse LLM response as JSON: %w; content: %s", err, response.Content)
	}

	var labels []string

	// NOTE: Label assignment for quote request emails.
	//
	// The LLM classifies emails using two booleans (is_batch, is_ltl), producing four possible combinations.
	// However, we only support three labels today:
	//   - quote_request:       Standard FTL single-shipment quote
	//   - batch_quote_request: FTL quote with multiple shipments
	//   - ltl_quote_request:   LTL (Less-Than Truckload) quote
	//
	// A fourth label (batch_ltl_quote_request) is not yet supported. When both is_batch and is_ltl are true, we
	// prioritize LTL since it requires specialized handling.
	//
	// Decision matrix (assuming both feature flags enabled):
	//
	//	| IsBatch | IsLTL | Label               |
	//	|---------|-------|---------------------|
	//	| false   | true  | ltl_quote_request   |
	//	| true    | false | batch_quote_request |
	//	| true    | true  | ltl_quote_request   | <- LTL takes priority
	//	| false   | false | quote_request       | <- Default fallback
	//
	// IMPORTANT: This switch is intentionally mutually exclusive to ensure exactly one label is
	// assigned per email.
	switch {
	case result.IsLTL && isLTLQuickQuoteEnabled:
		labels = append(labels, string(LTLQuoteRequestLabel))
	case result.IsBatch && isQuickQuoteEnabled:
		labels = append(labels, string(BatchQuoteRequestLabel))
	case isQuickQuoteEnabled || isLTLQuickQuoteEnabled:
		labels = append(labels, string(QuoteRequestLabel))
	}

	return labels, nil
}
