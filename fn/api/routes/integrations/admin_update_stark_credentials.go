package integrations

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds/integration"
)

type UpdateStarkCredentialsRequest struct {
	IntegrationID  uint   `json:"integrationId" validate:"required"`
	UserID         string `json:"userID"`
	RogersRevision string `json:"rogersRevision"`
	APIKey         string `json:"apiKey"`
}

type UpdateStarkCredentialsResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// AdminUpdateStarkCredentials updates the credentials for a Stark integration
func AdminUpdateStarkCredentials(c *fiber.Ctx) error {
	ctx := c.UserContext()
	email := middleware.ClaimsFromContext(c).Email

	// Check if user is admin
	if !perms.IsAdmin(email) && email != "<EMAIL>" {
		log.WarnNoSentry(
			ctx,
			"unauthorized: non-admin user trying to update stark credentials",
			zap.String("email", email),
		)
		return c.SendStatus(http.StatusUnauthorized)
	}

	var req UpdateStarkCredentialsRequest
	if err := c.BodyParser(&req); err != nil {
		log.Error(ctx, "failed to parse request body", zap.Error(err))
		return c.Status(http.StatusBadRequest).JSON(UpdateStarkCredentialsResponse{
			Success: false,
			Message: "Invalid request format",
		})
	}

	// Optional: verify it is a Stark integration
	integ, err := integration.Get(ctx, req.IntegrationID)
	if err != nil {
		log.Error(ctx, "failed to get integration", zap.Error(err))
		return c.Status(http.StatusInternalServerError).JSON(UpdateStarkCredentialsResponse{
			Success: false,
			Message: "Failed to get integration",
		})
	} else if integ.Name != models.Stark {
		return c.Status(http.StatusBadRequest).JSON(UpdateStarkCredentialsResponse{
			Success: false,
			Message: "Access denied: integration is not Stark type",
		})
	}

	// Update the Stark credentials
	err = integration.UpdateStarkCredentials(
		ctx,
		req.IntegrationID,
		req.UserID,
		req.RogersRevision,
		req.APIKey,
	)
	if err != nil {
		log.Error(
			ctx,
			"failed to update stark credentials",
			zap.Error(err),
			zap.Uint("integrationID", req.IntegrationID),
		)
		return c.Status(http.StatusInternalServerError).JSON(UpdateStarkCredentialsResponse{
			Success: false,
			Message: "Failed to update stark credentials",
		})
	}

	log.Info(
		ctx,
		"stark credentials updated successfully",
		zap.Uint("integrationID", req.IntegrationID),
		zap.String("adminEmail", email),
	)

	return c.JSON(UpdateStarkCredentialsResponse{
		Success: true,
		Message: "Stark credentials updated successfully",
	})
}
